<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="tiposBasico_v1.03.xsd"/>
	<xs:simpleType name="TNSU">
		<xs:annotation>
			<xs:documentation> Tipo número sequencial único do AN</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TConsNFeDest">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de Consulta de Nota Fiscal Eletrônica</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xServ">
				<xs:annotation>
					<xs:documentation>Serviço Solicitado</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:enumeration value="CONSULTAR NFE DEST"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CNPJ" type="TCnpj">
				<xs:annotation>
					<xs:documentation>CNPJ do destinatário da NF-e</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="indNFe">
				<xs:annotation>
					<xs:documentation>Indicador de NF-e consultada:
0 – Todas as NF-e;
1 – Somente as NF-e ainda não confirmadas.
2 – Somente as NF-e ainda não confirmadas (inclusive Ciência).
</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:enumeration value="0"/>
						<xs:enumeration value="1"/>
						<xs:enumeration value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="indEmi">
				<xs:annotation>
					<xs:documentation>Indicador do Emissor da NF-e:
0 – Todos os Emitentes;
1 – Somente as NF-e emitidas por emissores que não tenham a mesma raiz do CNPJ (para excluir as notas fiscais de transferência entre filiais).</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:enumeration value="0"/>
						<xs:enumeration value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ultNSU">
				<xs:annotation>
					<xs:documentation>Último NSU recebido. Caso seja informado com zero, a consulta deve ser realizada no universo das notas fiscais tenham sido recepcionadas nos últimos 30 dias.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,15}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVeConsNFeDest" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetConsNFeDest">
		<xs:annotation>
			<xs:documentation>Tipo Retorno do pedido de Consulta de Nota Fiscal Eletrônica</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que processou a NF-e</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dhResp" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>Data e hora da resposta à solicitação, no formato AAAA-MM-DDTHH:MM:SS</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="indCont" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicador de continuidade. Indica que o ambiente consultado POSSUI ainda documentos não pesquisados. 0 = NÃO possui documentos. 1 = Possui documentos.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:enumeration value="0"/>
						<xs:enumeration value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ultNSU" minOccurs="0">
				<xs:annotation>
					<xs:documentation>último NSU pesquisado, deve ser informado pelo WS, sempre que realizar alguma pesquisa para que o solicitante possa atualizar o último NSU pesquisado.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[1-9][0-9]{0,14}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="ret" minOccurs="0" maxOccurs="50">
				<xs:annotation>
					<xs:documentation>Resumo de NF-e ou CC-e</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:choice>
						<xs:element name="resNFe">
							<xs:annotation>
								<xs:documentation>Informações resumo da NF-e localizadas</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="chNFe" type="TChNFe">
										<xs:annotation>
											<xs:documentation>Chaves de acesso da NF-e consultada</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>CNPJ do Remetente da NF-e</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>CPF do Remetente da NF-e</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão Social ou Nome do Remetente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="3"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="IE" type="TIe">
										<xs:annotation>
											<xs:documentation>IE do Remetente. Valores válidos: vazio (não contribuinte do ICMS), ISENTO (contribuinte  do ICMS ISENTO de Inscrição no Cadastro de Contribuintes) ou IE (Contribuinte do ICMS)</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="dEmi" type="TData">
										<xs:annotation>
											<xs:documentation>Data de Emissão da NF-e</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpNF">
										<xs:annotation>
											<xs:documentation>Tipo do Documento Fiscal (0 - entrada; 1 - saída)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="vNF" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor Total da NF-e</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="digVal">
										<xs:annotation>
											<xs:documentation>Digest Value da NF-e na base de dados da SEFAZ </xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:length value="28"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="dhRecbto" type="xs:dateTime">
										<xs:annotation>
											<xs:documentation>Data e hora de processamento de autorização, no formato AAAA-MM-DDTHH:MM:SS.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cSitNFe">
										<xs:annotation>
											<xs:documentation>Situação da NF-e
1-Uso autorizado no momento da consulta;
2-Uso denegado;
3-NF-e cancelada;</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cSitConf">
										<xs:annotation>
											<xs:documentation>Situação Confirmação do Destinatário:
0 – sem manifestacao;
1 – confirmada;
2 – desconhecida;
3 – recusada
4 - ciência. </xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
												<xs:enumeration value="4"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="NSU" type="TNSU" use="required"/>
							</xs:complexType>
						</xs:element>
						<xs:element name="resCanc">
							<xs:annotation>
								<xs:documentation>Informações resumo da NF-e canceladas localizadas</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="chNFe" type="TChNFe">
										<xs:annotation>
											<xs:documentation>Chaves de acesso da NF-e consultada</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>CNPJ do Remetente da NF-e</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>CPF do Remetente da NF-e</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão Social ou Nome do Remetente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="3"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="IE" type="TIe">
										<xs:annotation>
											<xs:documentation>IE do Remetente. Valores válidos: vazio (não contribuinte do ICMS), ISENTO (contribuinte  do ICMS ISENTO de Inscrição no Cadastro de Contribuintes) ou IE (Contribuinte do ICMS)</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="dEmi" type="TData">
										<xs:annotation>
											<xs:documentation>Data de Emissão da NF-e</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpNF">
										<xs:annotation>
											<xs:documentation>Tipo do Documento Fiscal (0 - entrada; 1 - saída)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="vNF" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor Total da NF-e</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="digVal">
										<xs:annotation>
											<xs:documentation>Digest Value da NF-e na base de dados da SEFAZ </xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:length value="28"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="dhRecbto" type="xs:dateTime">
										<xs:annotation>
											<xs:documentation>Data e hora de processamento de autorização, no formato AAAA-MM-DDTHH:MM:SS.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cSitNFe">
										<xs:annotation>
											<xs:documentation>Situação da NF-e
3-NF-e cancelada;</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="3"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cSitConf">
										<xs:annotation>
											<xs:documentation>Situação Confirmação do Destinatário:
0 – sem manifestacao;
1 – confirmada;
2 – desconhecida;
3 – recusada
4 - ciência.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
												<xs:enumeration value="4"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="NSU" type="TNSU" use="required"/>
							</xs:complexType>
						</xs:element>
						<xs:element name="resCCe">
							<xs:annotation>
								<xs:documentation>Informações da carta de correção da NF-e localizadas</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="chNFe" type="TChNFe">
										<xs:annotation>
											<xs:documentation>Chave de Acesso da NF-e vinculada ao evento</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="dhEvento">
										<xs:annotation>
											<xs:documentation>Data e Hora do Evento, formato UTC (AAAA-MM-DDThh:mm:ssTZD, onde TZD = +hh:mm ou -hh:mm)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d-0[1-4]:00"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpEvento">
										<xs:annotation>
											<xs:documentation>Tipo do Evento</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{6}"/>
												<xs:enumeration value="110110"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="nSeqEvento">
										<xs:annotation>
											<xs:documentation>Seqüencial do evento para o mesmo tipo de evento.  Para maioria dos eventos será 1, nos casos em que possa existir mais de um evento, como é o caso da carta de correção, o autor do evento deve numerar de forma seqüencial.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[1-9]|[1][0-9]{0,1}|20"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="descEvento">
										<xs:annotation>
											<xs:documentation>Descrição do Evento - “Carta de Correção”</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="Carta de Correção"/>
												<xs:enumeration value="Carta de Correcao"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xCorrecao" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Correção a ser considerada</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:minLength value="15"/>
												<xs:maxLength value="1000"/>
												<xs:pattern value="[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpNF">
										<xs:annotation>
											<xs:documentation>Tipo do Documento Fiscal (0 - entrada; 1 - saída)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="dhRecbto" type="xs:dateTime">
										<xs:annotation>
											<xs:documentation>Data e hora de processamento de autorização, no formato AAAA-MM-DDTHH:MM:SS.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="NSU" type="TNSU" use="required"/>
							</xs:complexType>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVeConsNFeDest" use="required"/>
	</xs:complexType>
	<xs:simpleType name="TVeConsNFeDest">
		<xs:annotation>
			<xs:documentation> Tipo Versão da consultaNFeDest</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="1.01"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
