<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
	<xs:include schemaLocation="tiposBasico_v1.03.xsd"/>
	<xs:complexType name="TEvento">
		<xs:annotation>
			<xs:documentation>Tipo Evento</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infEvento">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="cOrgao" type="TCOrgaoIBGE">
							<xs:annotation>
								<xs:documentation>Código do órgão de recepção do Evento. Utilizar a Tabela do IBGE extendida, utilizar 90 para identificar o Ambiente Nacional</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:choice>
							<xs:annotation>
								<xs:documentation>Identificação do  autor do evento</xs:documentation>
							</xs:annotation>
							<xs:element name="CNPJ" type="TCnpjOpc">
								<xs:annotation>
									<xs:documentation>CNPJ</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="CPF" type="TCpf">
								<xs:annotation>
									<xs:documentation>CPF</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:choice>
						<xs:element name="chNFe" type="TChNFe">
							<xs:annotation>
								<xs:documentation>Chave de Acesso da NF-e vinculada ao evento</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="dhEvento" type="TDateTimeUTC">
							<xs:annotation>
								<xs:documentation>Data e Hora do Evento, formato UTC (AAAA-MM-DDThh:mm:ssTZD, onde TZD = +hh:mm ou -hh:mm)</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="tpEvento">
							<xs:annotation>
								<xs:documentation>Tipo do Evento</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="[0-9]{6}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="nSeqEvento">
							<xs:annotation>
								<xs:documentation>Seqüencial do evento para o mesmo tipo de evento.  Para maioria dos eventos será 1, nos casos em que possa existir mais de um evento, como é o caso da carta de correção, o autor do evento deve numerar de forma seqüencial.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="[1-9][0-9]{0,1}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="verEvento">
							<xs:annotation>
								<xs:documentation>Versão do Tipo do Evento</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="detEvento">
							<xs:complexType>
								<xs:sequence>
									<xs:any processContents="skip" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation>informações específicas do evento</xs:documentation>
										</xs:annotation>
									</xs:any>
								</xs:sequence>
								<xs:anyAttribute processContents="skip"/>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" use="required">
						<xs:annotation>
							<xs:documentation>Identificador da TAG a ser assinada, a regra de formação do Id é:
“ID” + tpEvento +  chave da NF-e + nSeqEvento</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="ID[0-9]{52}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEvento" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetEvento">
		<xs:annotation>
			<xs:documentation>Tipo retorno do Evento</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infEvento">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="verAplic" type="TVerAplic">
							<xs:annotation>
								<xs:documentation>Versão do Aplicativo que recebeu o Evento</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cOrgao" type="TCOrgaoIBGE">
							<xs:annotation>
								<xs:documentation>Código do órgão de recepção do Evento. Utilizar a Tabela do IBGE extendida, utilizar 90 para identificar o Ambiente Nacional</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cStat" type="TStat">
							<xs:annotation>
								<xs:documentation>Código do status da registro do Evento</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xMotivo" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Descrição literal do status do registro do Evento</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="chNFe" type="TChNFe" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Chave de Acesso NF-e vinculada</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="tpEvento" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Tipo do Evento vinculado</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="[0-9]{6}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="xEvento" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Descrição do Evento</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TString">
									<xs:minLength value="5"/>
									<xs:maxLength value="60"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="nSeqEvento" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Seqüencial do evento</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="[1-9][0-9]{0,1}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="cOrgaoAutor" type="TCOrgaoIBGE" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Código do órgão de autor do Evento. Utilizar a Tabela do IBGE extendida, utilizar 90 para identificar o Ambiente Nacional</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:choice minOccurs="0">
							<xs:annotation>
								<xs:documentation>Identificação do  destinatpario da NF-e</xs:documentation>
							</xs:annotation>
							<xs:element name="CNPJDest" type="TCnpjOpc">
								<xs:annotation>
									<xs:documentation>CNPJ Destinatário</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="CPFDest" type="TCpf">
								<xs:annotation>
									<xs:documentation>CPF Destiantário</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:choice>
						<xs:element name="emailDest" minOccurs="0">
							<xs:annotation>
								<xs:documentation>email do destinatário</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TString">
									<xs:minLength value="1"/>
									<xs:maxLength value="60"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="dhRegEvento">
							<xs:annotation>
								<xs:documentation>Data e Hora de registro do evento formato UTC AAAA-MM-DDTHH:MM:SSTZD</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d[\-,\+](0[0-9]|10|11|12):00"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="nProt" type="TProt" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número do protocolo de registro do evento</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" use="optional">
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="ID[0-9]{15}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEvento" use="required"/>
	</xs:complexType>
	<xs:complexType name="TEnvEvento">
		<xs:annotation>
			<xs:documentation> Tipo Lote de Envio</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="idLote">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,15}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="evento" type="TEvento" maxOccurs="20"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEnvEvento" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetEnvEvento">
		<xs:annotation>
			<xs:documentation> Tipo Retorno de Lote de Envio</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="idLote">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,15}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que recebeu o Evento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cOrgao" type="TCOrgaoIBGE">
				<xs:annotation>
					<xs:documentation>Código do òrgao que registrou o Evento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>Código do status da registro do Evento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do registro do Evento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="retEvento" type="TRetEvento" minOccurs="0" maxOccurs="20"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEnvEvento" use="required"/>
	</xs:complexType>
	<xs:complexType name="TProcEvento">
		<xs:annotation>
			<xs:documentation>Tipo procEvento</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="evento" type="TEvento"/>
			<xs:element name="retEvento" type="TRetEvento"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEvento" use="required"/>
	</xs:complexType>
	<xs:simpleType name="TVerEnvEvento">
		<xs:annotation>
			<xs:documentation>Tipo Versão do EnvEvento</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="1\.00"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TVerEvento">
		<xs:annotation>
			<xs:documentation>Tipo Versão do Evento</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="1\.00"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
