<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
	<xs:include schemaLocation="tiposBasico_v1.03.xsd"/>
	<xs:complexType name="TEvento">
		<xs:annotation>
			<xs:documentation>Tipo Evento</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infEvento">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="cOrgao" type="TCOrgaoIBGE">
							<xs:annotation>
								<xs:documentation>Código do órgão de recepção do Evento. Utilizar a Tabela do IBGE extendida, utilizar 90 para identificar o Ambiente Nacional</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:choice>
							<xs:annotation>
								<xs:documentation>Identificação do  autor do evento</xs:documentation>
							</xs:annotation>
							<xs:element name="CNPJ" type="TCnpjOpc">
								<xs:annotation>
									<xs:documentation>CNPJ</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="CPF" type="TCpf">
								<xs:annotation>
									<xs:documentation>CPF</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:choice>
						<xs:element name="chNFe" type="TChNFe">
							<xs:annotation>
								<xs:documentation>Chave de Acesso da NF-e vinculada ao evento</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="dhEvento" type="TDateTimeUTC">
							<xs:annotation>
								<xs:documentation>Data de emissão no formato UTC.  AAAA-MM-DDThh:mm:ssTZD</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="tpEvento">
							<xs:annotation>
								<xs:documentation>Tipo do Evento</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="[0-9]{6}"/>
									<xs:enumeration value="990910"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="nSeqEvento">
							<xs:annotation>
								<xs:documentation>Seqüencial do evento para o mesmo tipo de evento.  Para maioria dos eventos será 1, nos casos em que possa existir mais de um evento, como é o caso da carta de correção, o autor do evento deve numerar de forma seqüencial.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="[1-9]|[1][0-9]{0,1}|20"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="verEvento">
							<xs:annotation>
								<xs:documentation>Versão do Tipo do Evento</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="1.00"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="detEvento">
							<xs:annotation>
								<xs:documentation>Schema XML de validação do evento doVistoria SUFRAMA 990900</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="descEvento">
										<xs:annotation>
											<xs:documentation>Descrição do Evento - “Vistoria SUFRAMA”</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="Vistoria SUFRAMA"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="PINe">
										<xs:annotation>
											<xs:documentation>Número do PIN-e -  Protocolo de Internalização de Mercadoria Nacional eletronico</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{1,9}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="dVistoria">
										<xs:annotation>
											<xs:documentation>Data de ocorrência da vistoria, formato UTC (AAAA-MM-DDThh:mm:ssTZD, onde TZD = +hh:mm ou -hh:mm)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d-0[1-4]:00"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="locVistoria">
										<xs:annotation>
											<xs:documentation>Localidade onde ocorreu a vistoria</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="5"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="postoVistoria">
										<xs:annotation>
											<xs:documentation>Nome Posto do ponto onde ocorreu a vistoria</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="5"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xHistorico">
										<xs:annotation>
											<xs:documentation>Histórico da ocorrência, se existir.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="1024"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="versao" use="required">
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:whiteSpace value="preserve"/>
											<xs:enumeration value="1.00"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" use="required">
						<xs:annotation>
							<xs:documentation>Identificador da TAG a ser assinada, a regra de formação do Id é:
“ID” + tpEvento +  chave da NF-e + nSeqEvento</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="ID[0-9]{52}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEvento" use="required"/>
	</xs:complexType>
	<xs:element name="descEvento">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:enumeration value="Registro Passagem NFe"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="cOrgaoAutor" type="TCOrgaoIBGE"/>
	<xs:element name="cPostoUF">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:pattern value="[0-9]{4}"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="xPostoUF">
		<xs:simpleType>
			<xs:restriction base="TString">
				<xs:minLength value="2"/>
				<xs:maxLength value="60"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="latGPS">
		<xs:simpleType>
			<xs:restriction base="TString">
				<xs:pattern value="[0-9]\.[0-9]{6}|[1-8][0-9]\.[0-9]{6}|90\.[0-9]{6}|-[0-9]\.[0-9]{6}|-[1-8][0-9]\.[0-9]{6}|-90\.[0-9]{6}"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="longGPS">
		<xs:simpleType>
			<xs:restriction base="TString">
				<xs:pattern value="[0-9]\.[0-9]{6}|[1-9][0-9]\.[0-9]{6}|1[0-7][0-9]\.[0-9]{6}|180\.[0-9]{6}|-[0-9]\.[0-9]{6}|-[1-9][0-9]\.[0-9]{6}|-1[0-7][0-9]\.[0-9]{6}|-180\.[0-9]{6}"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="CPFOper" type="TCpf"/>
	<xs:element name="xNomeOper">
		<xs:simpleType>
			<xs:restriction base="TString">
				<xs:minLength value="2"/>
				<xs:maxLength value="60"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="indOffline">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:enumeration value="1"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="dhPas" type="TDateTimeUTC"/>
	<xs:element name="sentidoVia">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:enumeration value="E"/>
				<xs:enumeration value="S"/>
				<xs:enumeration value="I"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="indRet">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:enumeration value="R"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="UFDest" type="TUf"/>
	<xs:element name="xObs">
		<xs:simpleType>
			<xs:restriction base="TString">
				<xs:minLength value="1"/>
				<xs:maxLength value="255"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="chMDFe">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:pattern value="[0-9]{44}"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="chCTe">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:pattern value="[0-9]{44}"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="modalRodov">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="placaVeic" minOccurs="0"/>
				<xs:element ref="UFVeic" minOccurs="0"/>
				<xs:element ref="placaCarreta" minOccurs="0"/>
				<xs:element ref="UFCarreta" minOccurs="0"/>
				<xs:element ref="placaCarreta2" minOccurs="0"/>
				<xs:element ref="UFCarreta2" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="placaVeic" type="TPlaca"/>
	<xs:element name="UFVeic" type="TUf"/>
	<xs:element name="placaCarreta" type="TPlaca"/>
	<xs:element name="UFCarreta" type="TUf"/>
	<xs:element name="placaCarreta2" type="TPlaca"/>
	<xs:element name="UFCarreta2" type="TUf"/>
	<xs:element name="modalOutro">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="tpModal"/>
				<xs:element ref="xIdent" minOccurs="0"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="tpModal">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:enumeration value="F"/>
				<xs:enumeration value="D"/>
				<xs:enumeration value="AE"/>
				<xs:enumeration value="AQ"/>
				<xs:enumeration value="O"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="xIdent">
		<xs:simpleType>
			<xs:restriction base="TString">
				<xs:minLength value="1"/>
				<xs:maxLength value="255"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="ctg">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="nFormSeg" minOccurs="0"/>
				<xs:element ref="UFDest"/>
				<xs:element ref="tpEmis"/>
				<xs:choice>
					<xs:element ref="CNPJDest"/>
					<xs:element ref="CPFDest"/>
				</xs:choice>
				<xs:element ref="vTotalNFe"/>
				<xs:element ref="indICMS"/>
				<xs:element ref="indICMSST"/>
				<xs:element ref="diaEmi"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="nFormSeg">
		<xs:simpleType>
			<xs:restriction base="TString">
				<xs:minLength value="1"/>
				<xs:maxLength value="11"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="tpEmis">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:enumeration value="2"/>
				<xs:enumeration value="5"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="CNPJDest" type="TCnpj"/>
	<xs:element name="CPFDest" type="TCpf"/>
	<xs:element name="vTotalNFe" type="TDec_1302"/>
	<xs:element name="indICMS">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:enumeration value="1"/>
				<xs:enumeration value="2"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="indICMSST">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:enumeration value="1"/>
				<xs:enumeration value="2"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:element name="diaEmi">
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:whiteSpace value="preserve"/>
				<xs:pattern value="[1-9]|[1][0-9]|[2][0-9]|[3][0-1]?"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>
	<xs:simpleType name="TVerEvento">
		<xs:annotation>
			<xs:documentation>Tipo Versão do Evento</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="1\.00"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TretEvento">
		<xs:annotation>
			<xs:documentation>Tipo retorno do Evento</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infEvento">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="verAplic" type="TVerAplic">
							<xs:annotation>
								<xs:documentation>Versão do Aplicativo que recebeu o Evento</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cOrgao" type="TCOrgaoIBGE">
							<xs:annotation>
								<xs:documentation>Código do órgão de recepção do Evento. Utilizar a Tabela do IBGE extendida, utilizar 90 para identificar o Ambiente Nacional</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cStat" type="TStat">
							<xs:annotation>
								<xs:documentation>Código do status da registro do Evento</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xMotivo" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Descrição literal do status do registro do Evento</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="chNFe" type="TChNFe" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Chave de Acesso NF-e vinculada</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="tpEvento" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Tipo do Evento vinculado</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="[0-9]{6}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="xEvento" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Descrição do Evento</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TString">
									<xs:minLength value="5"/>
									<xs:maxLength value="60"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="nSeqEvento" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Seqüencial do evento</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="[1-9][0-9]{0,1}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:choice minOccurs="0">
							<xs:annotation>
								<xs:documentation>Identificação do  destinatário da NF-e</xs:documentation>
							</xs:annotation>
							<xs:element name="CNPJDest" type="TCnpjOpc">
								<xs:annotation>
									<xs:documentation>CNPJ Destinatário</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="CPFDest" type="TCpf">
								<xs:annotation>
									<xs:documentation>CPF Destiantário</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:choice>
						<xs:element name="emailDest" minOccurs="0">
							<xs:annotation>
								<xs:documentation>email do destinatário</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TString">
									<xs:minLength value="1"/>
									<xs:maxLength value="60"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="dhRegEvento">
							<xs:annotation>
								<xs:documentation>Data e Hora de do recebimento do evento ou do registro do evento formato UTC AAAA-MM-DDThh:mm:ssTZD.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d-0[1-4]:00"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="nProt" type="TProt" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número do protocolo de registro do evento</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" use="optional">
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="ID[0-9]{15}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEvento" use="required"/>
	</xs:complexType>
	<xs:complexType name="TEnvEvento">
		<xs:annotation>
			<xs:documentation> Tipo Lote de Envio</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="idLote">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,15}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="evento" type="TEvento" maxOccurs="20"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEnvEvento" use="required"/>
	</xs:complexType>
	<xs:simpleType name="TVerEnvEvento">
		<xs:annotation>
			<xs:documentation>Tipo Versão do EnvEvento</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="1\.00"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TRetEnvEvento">
		<xs:annotation>
			<xs:documentation> Tipo Retorno de Lote de Envio</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="idLote">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,15}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que recebeu o Evento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cOrgao" type="TCOrgaoIBGE">
				<xs:annotation>
					<xs:documentation>Código do òrgao que registrou o Evento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>Código do status da registro do Evento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do registro do Evento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="retEvento" type="TretEvento" minOccurs="0" maxOccurs="20"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEnvEvento" use="required"/>
	</xs:complexType>
	<xs:complexType name="TProcEvento">
		<xs:annotation>
			<xs:documentation>Tipo procEvento</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="evento" type="TEvento"/>
			<xs:element name="retEvento" type="TretEvento"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerEvento" use="required"/>
	</xs:complexType>
	<xs:simpleType name="TCOrgaoIBGE">
		<xs:annotation>
			<xs:documentation>Tipo Código de orgão (UF da tabela do IBGE + 90 RFB)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="11"/>
			<xs:enumeration value="12"/>
			<xs:enumeration value="13"/>
			<xs:enumeration value="14"/>
			<xs:enumeration value="15"/>
			<xs:enumeration value="16"/>
			<xs:enumeration value="17"/>
			<xs:enumeration value="21"/>
			<xs:enumeration value="22"/>
			<xs:enumeration value="23"/>
			<xs:enumeration value="24"/>
			<xs:enumeration value="25"/>
			<xs:enumeration value="26"/>
			<xs:enumeration value="27"/>
			<xs:enumeration value="28"/>
			<xs:enumeration value="29"/>
			<xs:enumeration value="31"/>
			<xs:enumeration value="32"/>
			<xs:enumeration value="33"/>
			<xs:enumeration value="35"/>
			<xs:enumeration value="41"/>
			<xs:enumeration value="42"/>
			<xs:enumeration value="43"/>
			<xs:enumeration value="50"/>
			<xs:enumeration value="51"/>
			<xs:enumeration value="52"/>
			<xs:enumeration value="53"/>
			<xs:enumeration value="90"/>
			<xs:enumeration value="91"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
