# Configuration example.
#
# For detailed information about these directives see
# <URL:http://httpd.apache.org/docs/2.4/mod/mod_proxy_html.html>
# and for mod_xml2enc see
# <URL:http://httpd.apache.org/docs/2.4/mod/mod_xml2enc.html>
#
# First, to load the module with its prerequisites.  Note: mod_xml2enc
# is not always necessary, but without it mod_proxy_html is likely to
# mangle pages in encodings other than ASCII or Unicode (utf-8).
#
# For Unix-family systems:
# LoadFile	/usr/lib/libxml2.so
# LoadModule	proxy_html_module	modules/mod_proxy_html.so
# LoadModule	xml2enc_module		modules/mod_xml2enc.so
#
# For Windows (I don't know if there's a standard path for the libraries)
# LoadFile	C:/path/zlib.dll
# LoadFile	C:/path/iconv.dll
# LoadFile	C:/path/libxml2.dll
# LoadModule	proxy_html_module	modules/mod_proxy_html.so
# LoadModule	xml2enc_module		modules/mod_xml2enc.so
# 
# All knowledge of HTML links has been removed from the mod_proxy_html
# code itself, and is instead read from httpd.conf (or included file)
# at server startup.  So you MUST declare it.  This will normally be
# at top level, but can also be used in a <Location>.
#
# Here's the declaration for W3C HTML 4.01 and XHTML 1.0

ProxyHTMLLinks	a		href
ProxyHTMLLinks	area		href
ProxyHTMLLinks	link		href
ProxyHTMLLinks	img		src longdesc usemap
ProxyHTMLLinks	object		classid codebase data usemap
ProxyHTMLLinks	q		cite
ProxyHTMLLinks	blockquote	cite
ProxyHTMLLinks	ins		cite
ProxyHTMLLinks	del		cite
ProxyHTMLLinks	form		action
ProxyHTMLLinks	input		src usemap
ProxyHTMLLinks	head		profile
ProxyHTMLLinks	base		href
ProxyHTMLLinks	script		src for

# To support scripting events (with ProxyHTMLExtended On),
# you'll need to declare them too.

ProxyHTMLEvents	onclick ondblclick onmousedown onmouseup \
		onmouseover onmousemove onmouseout onkeypress \
		onkeydown onkeyup onfocus onblur onload \
		onunload onsubmit onreset onselect onchange

# If you need to support legacy (pre-1998, aka "transitional") HTML or XHTML,
# you'll need to uncomment the following deprecated link attributes.
# Note that these are enabled in earlier mod_proxy_html versions
#
# ProxyHTMLLinks	frame		src longdesc
# ProxyHTMLLinks	iframe		src longdesc
# ProxyHTMLLinks	body		background
# ProxyHTMLLinks	applet		codebase
#
# If you're dealing with proprietary HTML variants,
# declare your own URL attributes here as required.
#
# ProxyHTMLLinks	myelement	myattr otherattr
#
###########
# EXAMPLE #
###########
#
# To define the URL /my-gateway/ as a gateway to an appserver with address
# http://some.app.intranet/ on a private network, after loading the
# modules and including this configuration file:
#
# ProxyRequests Off  <-- this is an important security setting
# ProxyPass /my-gateway/ http://some.app.intranet/
# <Location /my-gateway/>
#	ProxyPassReverse /
#	ProxyHTMLEnable On
#	ProxyHTMLURLMap http://some.app.intranet/ /my-gateway/
#	ProxyHTMLURLMap / /my-gateway/
# </Location>
#
# Many (though not all) real-life setups are more complex.
#
# See the documentation at
# http://apache.webthing.com/mod_proxy_html/
# and the tutorial at
# http://www.apachetutor.org/admin/reverseproxies
