<?xml version="1.0" encoding="UTF-8"?>
<!--  PL_006f versao com correcoes no xServ para tornar a literal STATUS obrigatoria 21/05/2010 -->
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns="http://www.portalfiscal.inf.br/nfe" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="tiposBasico_v4.00.xsd"/>
	<xs:complexType name="TConsStatServ">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de Consulta do Status do Serviço</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>Sigla da UF consultada</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xServ">
				<xs:annotation>
					<xs:documentation>Serviço Solicitado</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TServ">
						<xs:enumeration value="STATUS"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerConsStatServ" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetConsStatServ">
		<xs:annotation>
			<xs:documentation>Tipo Resultado da Consulta do Status do Serviço</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que processou a NF-e</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>Código da UF responsável pelo serviço</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dhRecbto" type="TDateTimeUTC">
				<xs:annotation>
					<xs:documentation>Data e hora do recebimento da consulta no formato AAAA-MM-DDTHH:MM:SSTZD</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="tMed" type="TMed" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Tempo médio de resposta do serviço (em segundos) dos últimos 5 minutos</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dhRetorno" type="TDateTimeUTC" minOccurs="0">
				<xs:annotation>
					<xs:documentation>AAAA-MM-DDTHH:MM:SSDeve ser preenchida com data e hora previstas para o retorno dos serviços prestados.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xObs" type="TMotivo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Campo observação utilizado para incluir informações ao contribuinte</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerConsStatServ" use="required"/>
	</xs:complexType>
	<xs:simpleType name="TVerConsStatServ">
		<xs:annotation>
			<xs:documentation>Tipo versão do leiuate da Consulta Status do Serviço 4.00</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:token">
			<xs:pattern value="4\.00"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
