﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
	<xs:include schemaLocation="tiposBasico_v3.10.xsd"/>
	<xs:element name="envEvento">
        <xs:complexType>
			<xs:sequence>
                <xs:element name="idLote">
                    <xs:annotation>
                        <xs:documentation>Identificador de controle do Lote de envio do Evento.</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:whiteSpace value="preserve" />
                            <xs:pattern value="[0-9]{1,15}" />
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="evento" maxOccurs="20">
                    <xs:complexType>
						<xs:sequence>
                            <xs:annotation>
                                <xs:documentation>Evento, um lote pode conter até 20 eventos</xs:documentation>
                            </xs:annotation>
                            <xs:element name="infEvento">
                                <xs:complexType>
									<xs:sequence>
                                        <xs:annotation>
                                            <xs:documentation>Grupo de informações do registro do Evento</xs:documentation>
                                        </xs:annotation>
                                        <xs:element name="cOrgao" type="TCOrgaoIBGE">
                                            <xs:annotation>
                                                <xs:documentation>Código do órgão de recepção do Evento.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="tpAmb" type="TAmb">
                                            <xs:annotation>
                                                <xs:documentation>Identificação do Ambiente: 1=Produção /2=Homologação</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:choice>
                                            <xs:annotation>
                                                <xs:documentation>Informar o CNPJ / CPF do autor do Evento.</xs:documentation>
                                            </xs:annotation>
                                            <xs:element name="CNPJ" type="TCnpjOpc">
                                                <xs:annotation>
                                                    <xs:documentation>CNPJ</xs:documentation>
                                                </xs:annotation>
                                            </xs:element>
                                            <xs:element name="CPF" type="TCpf">
                                                <xs:annotation>
                                                    <xs:documentation>CPF</xs:documentation>
                                                </xs:annotation>
                                            </xs:element>
                                        </xs:choice>
                                        <xs:element name="chNFe" type="TChNFe">
                                            <xs:annotation>
                                                <xs:documentation>Para o evento de EPEC, a posição 35 da Chave de Acesso deve ser 4 (tpEmis=4).</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="dhEvento" type="TDateTimeUTC">
                                            <xs:annotation>
                                                <xs:documentation>Data e hora do evento no formato AAAA-MM-DDThh:mm:ssTZD (UTC - Universal Coordinated Time).</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="tpEvento">
                                            <xs:annotation>
                                                <xs:documentation>Código do evento: 110140 -EPEC</xs:documentation>
                                            </xs:annotation>
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
													<xs:whiteSpace value="preserve"/>
													<xs:pattern value="[0-9]{6}"/>
													<xs:enumeration value="110140"/>
												</xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nSeqEvento">
                                            <xs:annotation>
                                                <xs:documentation>Informar o valor "1" para o evento do EPEC.</xs:documentation>
                                            </xs:annotation>
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                    <xs:whiteSpace value="preserve" />
                                                    <xs:pattern value="[0-9]{1,2}" />
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="verEvento" type="TVerEvento">
                                            <xs:annotation>
                                                <xs:documentation>Versão do detalhe do evento (grupo detEvento - P17), informação usada pela SEFAZ para validar o grupo detEvento.</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="detEvento">
                                            <xs:complexType>
												<xs:sequence>
                                                    <xs:annotation>
                                                        <xs:documentation>Informações de detalhes do evento</xs:documentation>
                                                    </xs:annotation>
                                                    <xs:element name="descEvento">
                                                        <xs:annotation>
                                                            <xs:documentation>"EPEC"</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:simpleType>
                                                            <xs:restriction base="xs:string">
                                                                <xs:enumeration value="EPEC"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="cOrgaoAutor" type="TCodUfIBGE">
                                                        <xs:annotation>
                                                            <xs:documentation>Código do Órgão do Autor do Evento.</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="tpAutor">
                                                        <xs:annotation>
                                                            <xs:documentation>Informar "1=Empresa Emitente" para este evento.</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:enumeration value="1"/>
															</xs:restriction>
														</xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="verAplic" type="TVerAplic">
                                                        <xs:annotation>
                                                            <xs:documentation>Versão do aplicativo do Autor do Evento.</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="dhEmi" type="TDateTimeUTC">
                                                        <xs:annotation>
                                                            <xs:documentation>Data e hora no formato UTC (Universal Coordinated Time): "AAAA-MM-DDThh:mm:ss TZD".</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="tpNF">
                                                        <xs:annotation>
                                                            <xs:documentation>Informar 1=Saída.</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:enumeration value="1"/>
															</xs:restriction>
														</xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="IE" type="TIe">
                                                        <xs:annotation>
                                                            <xs:documentation>IE do Emitente</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="dest" minOccurs="0">
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="UF" type="TUf">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Sigla da UF do destinatário. </xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                                <xs:choice>
                                                                    <xs:element name="CNPJ" type="TCnpjOpc">
                                                                        <xs:annotation>
                                                                            <xs:documentation>CNPJ</xs:documentation>
                                                                        </xs:annotation>
                                                                    </xs:element>
                                                                    <xs:element name="CPF" type="TCpf">
                                                                        <xs:annotation>
                                                                            <xs:documentation>CPF</xs:documentation>
                                                                        </xs:annotation>
                                                                    </xs:element>
                                                                    <xs:element name="idEstrangeiro">
                                                                        <xs:annotation>
                                                                            <xs:documentation>ID Estrangeiro</xs:documentation>
                                                                        </xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:pattern value="([!-ÿ]{0}|[!-ÿ]{5,20})?"/>
																			</xs:restriction>
																		</xs:simpleType>
                                                                    </xs:element>
                                                                </xs:choice>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="vNF" type="TDec_1302">
                                                        <xs:annotation>
                                                            <xs:documentation>Valor total da NFC-e</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="vICMS" type="TDec_1302">
                                                        <xs:annotation>
                                                            <xs:documentation>Valor total do ICMS</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                </xs:sequence>
												<xs:attribute name="versao" type="TVerEvento" use="required" />
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
									<xs:attribute name="Id" use="required">
										<xs:annotation>
											<xs:documentation>Identificador da TAG a ser assinada, a regra de formação do Id é: "ID" + tpEvento +  chave da NFC-e + nSeqEvento</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:ID">
												<xs:pattern value="ID[0-9]{52}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
                                </xs:complexType>
                            </xs:element>
                            <xs:element ref="ds:Signature"/>
                        </xs:sequence>
						<xs:attribute name="versao" type="TVerEvento" use="required" />
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
			<xs:attribute name="versao" type="TVerEvento" use="required" />
        </xs:complexType>
	</xs:element>
	<xs:simpleType name="TVerEvento">
		<xs:annotation>
			<xs:documentation>Versão do Tipo do Evento</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{1,2}\.[0-9]{1,2}"/>
		</xs:restriction>
	</xs:simpleType> 
    <xs:simpleType name="TCOrgaoIBGE">
		<xs:annotation>
			<xs:documentation>Tipo Código de orgão (UF da tabela do IBGE + 91 RFB)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="11"/>
			<xs:enumeration value="12"/>
			<xs:enumeration value="13"/>
			<xs:enumeration value="14"/>
			<xs:enumeration value="15"/>
			<xs:enumeration value="16"/>
			<xs:enumeration value="17"/>
			<xs:enumeration value="21"/>
			<xs:enumeration value="22"/>
			<xs:enumeration value="23"/>
			<xs:enumeration value="24"/>
			<xs:enumeration value="25"/>
			<xs:enumeration value="26"/>
			<xs:enumeration value="27"/>
			<xs:enumeration value="28"/>
			<xs:enumeration value="29"/>
			<xs:enumeration value="31"/>
			<xs:enumeration value="32"/>
			<xs:enumeration value="33"/>
			<xs:enumeration value="35"/>
			<xs:enumeration value="41"/>
			<xs:enumeration value="42"/>
			<xs:enumeration value="43"/>
			<xs:enumeration value="50"/>
			<xs:enumeration value="51"/>
			<xs:enumeration value="52"/>
			<xs:enumeration value="53"/>
			<xs:enumeration value="91"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>