/* Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _APACHE_NOPROBES_H_
#define _APACHE_NOPROBES_H_

#define AP_ACCESS_CHECKER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_ACCESS_CHECKER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_ACCESS_CHECKER_DISPATCH_INVOKE(arg0)
#define AP_ACCESS_CHECKER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_ACCESS_CHECKER_ENTRY()
#define AP_ACCESS_CHECKER_ENTRY_ENABLED() (0)
#define AP_ACCESS_CHECKER_RETURN(arg0)
#define AP_ACCESS_CHECKER_RETURN_ENABLED() (0)
#define AP_AUTH_CHECKER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_AUTH_CHECKER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_AUTH_CHECKER_DISPATCH_INVOKE(arg0)
#define AP_AUTH_CHECKER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_AUTH_CHECKER_ENTRY()
#define AP_AUTH_CHECKER_ENTRY_ENABLED() (0)
#define AP_AUTH_CHECKER_RETURN(arg0)
#define AP_AUTH_CHECKER_RETURN_ENABLED() (0)
#define AP_CANON_HANDLER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_CANON_HANDLER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_CANON_HANDLER_DISPATCH_INVOKE(arg0)
#define AP_CANON_HANDLER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_CANON_HANDLER_ENTRY()
#define AP_CANON_HANDLER_ENTRY_ENABLED() (0)
#define AP_CANON_HANDLER_RETURN(arg0)
#define AP_CANON_HANDLER_RETURN_ENABLED() (0)
#define AP_CHECK_USER_ID_DISPATCH_COMPLETE(arg0, arg1)
#define AP_CHECK_USER_ID_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_CHECK_USER_ID_DISPATCH_INVOKE(arg0)
#define AP_CHECK_USER_ID_DISPATCH_INVOKE_ENABLED() (0)
#define AP_CHECK_USER_ID_ENTRY()
#define AP_CHECK_USER_ID_ENTRY_ENABLED() (0)
#define AP_CHECK_USER_ID_RETURN(arg0)
#define AP_CHECK_USER_ID_RETURN_ENABLED() (0)
#define AP_CHILD_INIT_DISPATCH_COMPLETE(arg0, arg1)
#define AP_CHILD_INIT_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_CHILD_INIT_DISPATCH_INVOKE(arg0)
#define AP_CHILD_INIT_DISPATCH_INVOKE_ENABLED() (0)
#define AP_CHILD_INIT_ENTRY()
#define AP_CHILD_INIT_ENTRY_ENABLED() (0)
#define AP_CHILD_INIT_RETURN(arg0)
#define AP_CHILD_INIT_RETURN_ENABLED() (0)
#define AP_CREATE_CONNECTION_DISPATCH_COMPLETE(arg0, arg1)
#define AP_CREATE_CONNECTION_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_CREATE_CONNECTION_DISPATCH_INVOKE(arg0)
#define AP_CREATE_CONNECTION_DISPATCH_INVOKE_ENABLED() (0)
#define AP_CREATE_CONNECTION_ENTRY()
#define AP_CREATE_CONNECTION_ENTRY_ENABLED() (0)
#define AP_CREATE_CONNECTION_RETURN(arg0)
#define AP_CREATE_CONNECTION_RETURN_ENABLED() (0)
#define AP_CREATE_REQUEST_DISPATCH_COMPLETE(arg0, arg1)
#define AP_CREATE_REQUEST_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_CREATE_REQUEST_DISPATCH_INVOKE(arg0)
#define AP_CREATE_REQUEST_DISPATCH_INVOKE_ENABLED() (0)
#define AP_CREATE_REQUEST_ENTRY()
#define AP_CREATE_REQUEST_ENTRY_ENABLED() (0)
#define AP_CREATE_REQUEST_RETURN(arg0)
#define AP_CREATE_REQUEST_RETURN_ENABLED() (0)
#define AP_DEFAULT_PORT_DISPATCH_COMPLETE(arg0, arg1)
#define AP_DEFAULT_PORT_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_DEFAULT_PORT_DISPATCH_INVOKE(arg0)
#define AP_DEFAULT_PORT_DISPATCH_INVOKE_ENABLED() (0)
#define AP_DEFAULT_PORT_ENTRY()
#define AP_DEFAULT_PORT_ENTRY_ENABLED() (0)
#define AP_DEFAULT_PORT_RETURN(arg0)
#define AP_DEFAULT_PORT_RETURN_ENABLED() (0)
#define AP_ERROR_LOG_DISPATCH_COMPLETE(arg0, arg1)
#define AP_ERROR_LOG_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_ERROR_LOG_DISPATCH_INVOKE(arg0)
#define AP_ERROR_LOG_DISPATCH_INVOKE_ENABLED() (0)
#define AP_ERROR_LOG_ENTRY()
#define AP_ERROR_LOG_ENTRY_ENABLED() (0)
#define AP_ERROR_LOG_RETURN(arg0)
#define AP_ERROR_LOG_RETURN_ENABLED() (0)
#define AP_FIND_LIVEPROP_DISPATCH_COMPLETE(arg0, arg1)
#define AP_FIND_LIVEPROP_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_FIND_LIVEPROP_DISPATCH_INVOKE(arg0)
#define AP_FIND_LIVEPROP_DISPATCH_INVOKE_ENABLED() (0)
#define AP_FIND_LIVEPROP_ENTRY()
#define AP_FIND_LIVEPROP_ENTRY_ENABLED() (0)
#define AP_FIND_LIVEPROP_RETURN(arg0)
#define AP_FIND_LIVEPROP_RETURN_ENABLED() (0)
#define AP_FIXUPS_DISPATCH_COMPLETE(arg0, arg1)
#define AP_FIXUPS_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_FIXUPS_DISPATCH_INVOKE(arg0)
#define AP_FIXUPS_DISPATCH_INVOKE_ENABLED() (0)
#define AP_FIXUPS_ENTRY()
#define AP_FIXUPS_ENTRY_ENABLED() (0)
#define AP_FIXUPS_RETURN(arg0)
#define AP_FIXUPS_RETURN_ENABLED() (0)
#define AP_GATHER_PROPSETS_DISPATCH_COMPLETE(arg0, arg1)
#define AP_GATHER_PROPSETS_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_GATHER_PROPSETS_DISPATCH_INVOKE(arg0)
#define AP_GATHER_PROPSETS_DISPATCH_INVOKE_ENABLED() (0)
#define AP_GATHER_PROPSETS_ENTRY()
#define AP_GATHER_PROPSETS_ENTRY_ENABLED() (0)
#define AP_GATHER_PROPSETS_RETURN(arg0)
#define AP_GATHER_PROPSETS_RETURN_ENABLED() (0)
#define AP_GET_MGMT_ITEMS_DISPATCH_COMPLETE(arg0, arg1)
#define AP_GET_MGMT_ITEMS_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_GET_MGMT_ITEMS_DISPATCH_INVOKE(arg0)
#define AP_GET_MGMT_ITEMS_DISPATCH_INVOKE_ENABLED() (0)
#define AP_GET_MGMT_ITEMS_ENTRY()
#define AP_GET_MGMT_ITEMS_ENTRY_ENABLED() (0)
#define AP_GET_MGMT_ITEMS_RETURN(arg0)
#define AP_GET_MGMT_ITEMS_RETURN_ENABLED() (0)
#define AP_GET_SUEXEC_IDENTITY_DISPATCH_COMPLETE(arg0, arg1)
#define AP_GET_SUEXEC_IDENTITY_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_GET_SUEXEC_IDENTITY_DISPATCH_INVOKE(arg0)
#define AP_GET_SUEXEC_IDENTITY_DISPATCH_INVOKE_ENABLED() (0)
#define AP_GET_SUEXEC_IDENTITY_ENTRY()
#define AP_GET_SUEXEC_IDENTITY_ENTRY_ENABLED() (0)
#define AP_GET_SUEXEC_IDENTITY_RETURN(arg0)
#define AP_GET_SUEXEC_IDENTITY_RETURN_ENABLED() (0)
#define AP_HANDLER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_HANDLER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_HANDLER_DISPATCH_INVOKE(arg0)
#define AP_HANDLER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_HANDLER_ENTRY()
#define AP_HANDLER_ENTRY_ENABLED() (0)
#define AP_HANDLER_RETURN(arg0)
#define AP_HANDLER_RETURN_ENABLED() (0)
#define AP_HEADER_PARSER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_HEADER_PARSER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_HEADER_PARSER_DISPATCH_INVOKE(arg0)
#define AP_HEADER_PARSER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_HEADER_PARSER_ENTRY()
#define AP_HEADER_PARSER_ENTRY_ENABLED() (0)
#define AP_HEADER_PARSER_RETURN(arg0)
#define AP_HEADER_PARSER_RETURN_ENABLED() (0)
#define AP_HTTP_SCHEME_DISPATCH_COMPLETE(arg0, arg1)
#define AP_HTTP_SCHEME_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_HTTP_SCHEME_DISPATCH_INVOKE(arg0)
#define AP_HTTP_SCHEME_DISPATCH_INVOKE_ENABLED() (0)
#define AP_HTTP_SCHEME_ENTRY()
#define AP_HTTP_SCHEME_ENTRY_ENABLED() (0)
#define AP_HTTP_SCHEME_RETURN(arg0)
#define AP_HTTP_SCHEME_RETURN_ENABLED() (0)
#define AP_INSERT_ALL_LIVEPROPS_DISPATCH_COMPLETE(arg0, arg1)
#define AP_INSERT_ALL_LIVEPROPS_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_INSERT_ALL_LIVEPROPS_DISPATCH_INVOKE(arg0)
#define AP_INSERT_ALL_LIVEPROPS_DISPATCH_INVOKE_ENABLED() (0)
#define AP_INSERT_ALL_LIVEPROPS_ENTRY()
#define AP_INSERT_ALL_LIVEPROPS_ENTRY_ENABLED() (0)
#define AP_INSERT_ALL_LIVEPROPS_RETURN(arg0)
#define AP_INSERT_ALL_LIVEPROPS_RETURN_ENABLED() (0)
#define AP_INSERT_ERROR_FILTER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_INSERT_ERROR_FILTER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_INSERT_ERROR_FILTER_DISPATCH_INVOKE(arg0)
#define AP_INSERT_ERROR_FILTER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_INSERT_ERROR_FILTER_ENTRY()
#define AP_INSERT_ERROR_FILTER_ENTRY_ENABLED() (0)
#define AP_INSERT_ERROR_FILTER_RETURN(arg0)
#define AP_INSERT_ERROR_FILTER_RETURN_ENABLED() (0)
#define AP_INSERT_FILTER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_INSERT_FILTER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_INSERT_FILTER_DISPATCH_INVOKE(arg0)
#define AP_INSERT_FILTER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_INSERT_FILTER_ENTRY()
#define AP_INSERT_FILTER_ENTRY_ENABLED() (0)
#define AP_INSERT_FILTER_RETURN(arg0)
#define AP_INSERT_FILTER_RETURN_ENABLED() (0)
#define AP_INTERNAL_REDIRECT(arg0, arg1)
#define AP_INTERNAL_REDIRECT_ENABLED() (0)
#define AP_LOG_TRANSACTION_DISPATCH_COMPLETE(arg0, arg1)
#define AP_LOG_TRANSACTION_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_LOG_TRANSACTION_DISPATCH_INVOKE(arg0)
#define AP_LOG_TRANSACTION_DISPATCH_INVOKE_ENABLED() (0)
#define AP_LOG_TRANSACTION_ENTRY()
#define AP_LOG_TRANSACTION_ENTRY_ENABLED() (0)
#define AP_LOG_TRANSACTION_RETURN(arg0)
#define AP_LOG_TRANSACTION_RETURN_ENABLED() (0)
#define AP_MAP_TO_STORAGE_DISPATCH_COMPLETE(arg0, arg1)
#define AP_MAP_TO_STORAGE_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_MAP_TO_STORAGE_DISPATCH_INVOKE(arg0)
#define AP_MAP_TO_STORAGE_DISPATCH_INVOKE_ENABLED() (0)
#define AP_MAP_TO_STORAGE_ENTRY()
#define AP_MAP_TO_STORAGE_ENTRY_ENABLED() (0)
#define AP_MAP_TO_STORAGE_RETURN(arg0)
#define AP_MAP_TO_STORAGE_RETURN_ENABLED() (0)
#define AP_MONITOR_DISPATCH_COMPLETE(arg0, arg1)
#define AP_MONITOR_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_MONITOR_DISPATCH_INVOKE(arg0)
#define AP_MONITOR_DISPATCH_INVOKE_ENABLED() (0)
#define AP_MONITOR_ENTRY()
#define AP_MONITOR_ENTRY_ENABLED() (0)
#define AP_MONITOR_RETURN(arg0)
#define AP_MONITOR_RETURN_ENABLED() (0)
#define AP_OPEN_LOGS_DISPATCH_COMPLETE(arg0, arg1)
#define AP_OPEN_LOGS_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_OPEN_LOGS_DISPATCH_INVOKE(arg0)
#define AP_OPEN_LOGS_DISPATCH_INVOKE_ENABLED() (0)
#define AP_OPEN_LOGS_ENTRY()
#define AP_OPEN_LOGS_ENTRY_ENABLED() (0)
#define AP_OPEN_LOGS_RETURN(arg0)
#define AP_OPEN_LOGS_RETURN_ENABLED() (0)
#define AP_OPTIONAL_FN_RETRIEVE_DISPATCH_COMPLETE(arg0, arg1)
#define AP_OPTIONAL_FN_RETRIEVE_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_OPTIONAL_FN_RETRIEVE_DISPATCH_INVOKE(arg0)
#define AP_OPTIONAL_FN_RETRIEVE_DISPATCH_INVOKE_ENABLED() (0)
#define AP_OPTIONAL_FN_RETRIEVE_ENTRY()
#define AP_OPTIONAL_FN_RETRIEVE_ENTRY_ENABLED() (0)
#define AP_OPTIONAL_FN_RETRIEVE_RETURN(arg0)
#define AP_OPTIONAL_FN_RETRIEVE_RETURN_ENABLED() (0)
#define AP_POST_CONFIG_DISPATCH_COMPLETE(arg0, arg1)
#define AP_POST_CONFIG_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_POST_CONFIG_DISPATCH_INVOKE(arg0)
#define AP_POST_CONFIG_DISPATCH_INVOKE_ENABLED() (0)
#define AP_POST_CONFIG_ENTRY()
#define AP_POST_CONFIG_ENTRY_ENABLED() (0)
#define AP_POST_CONFIG_RETURN(arg0)
#define AP_POST_CONFIG_RETURN_ENABLED() (0)
#define AP_POST_READ_REQUEST_DISPATCH_COMPLETE(arg0, arg1)
#define AP_POST_READ_REQUEST_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_POST_READ_REQUEST_DISPATCH_INVOKE(arg0)
#define AP_POST_READ_REQUEST_DISPATCH_INVOKE_ENABLED() (0)
#define AP_POST_READ_REQUEST_ENTRY()
#define AP_POST_READ_REQUEST_ENTRY_ENABLED() (0)
#define AP_POST_READ_REQUEST_RETURN(arg0)
#define AP_POST_READ_REQUEST_RETURN_ENABLED() (0)
#define AP_POST_REQUEST_DISPATCH_COMPLETE(arg0, arg1)
#define AP_POST_REQUEST_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_POST_REQUEST_DISPATCH_INVOKE(arg0)
#define AP_POST_REQUEST_DISPATCH_INVOKE_ENABLED() (0)
#define AP_POST_REQUEST_ENTRY()
#define AP_POST_REQUEST_ENTRY_ENABLED() (0)
#define AP_POST_REQUEST_RETURN(arg0)
#define AP_POST_REQUEST_RETURN_ENABLED() (0)
#define AP_PRE_CONFIG_DISPATCH_COMPLETE(arg0, arg1)
#define AP_PRE_CONFIG_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_PRE_CONFIG_DISPATCH_INVOKE(arg0)
#define AP_PRE_CONFIG_DISPATCH_INVOKE_ENABLED() (0)
#define AP_PRE_CONFIG_ENTRY()
#define AP_PRE_CONFIG_ENTRY_ENABLED() (0)
#define AP_PRE_CONFIG_RETURN(arg0)
#define AP_PRE_CONFIG_RETURN_ENABLED() (0)
#define AP_PRE_CONNECTION_DISPATCH_COMPLETE(arg0, arg1)
#define AP_PRE_CONNECTION_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_PRE_CONNECTION_DISPATCH_INVOKE(arg0)
#define AP_PRE_CONNECTION_DISPATCH_INVOKE_ENABLED() (0)
#define AP_PRE_CONNECTION_ENTRY()
#define AP_PRE_CONNECTION_ENTRY_ENABLED() (0)
#define AP_PRE_CONNECTION_RETURN(arg0)
#define AP_PRE_CONNECTION_RETURN_ENABLED() (0)
#define AP_PRE_MPM_DISPATCH_COMPLETE(arg0, arg1)
#define AP_PRE_MPM_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_PRE_MPM_DISPATCH_INVOKE(arg0)
#define AP_PRE_MPM_DISPATCH_INVOKE_ENABLED() (0)
#define AP_PRE_MPM_ENTRY()
#define AP_PRE_MPM_ENTRY_ENABLED() (0)
#define AP_PRE_MPM_RETURN(arg0)
#define AP_PRE_MPM_RETURN_ENABLED() (0)
#define AP_PRE_REQUEST_DISPATCH_COMPLETE(arg0, arg1)
#define AP_PRE_REQUEST_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_PRE_REQUEST_DISPATCH_INVOKE(arg0)
#define AP_PRE_REQUEST_DISPATCH_INVOKE_ENABLED() (0)
#define AP_PRE_REQUEST_ENTRY()
#define AP_PRE_REQUEST_ENTRY_ENABLED() (0)
#define AP_PRE_REQUEST_RETURN(arg0)
#define AP_PRE_REQUEST_RETURN_ENABLED() (0)
#define AP_PROCESS_REQUEST_ENTRY(arg0, arg1)
#define AP_PROCESS_REQUEST_ENTRY_ENABLED() (0)
#define AP_PROCESS_REQUEST_RETURN(arg0, arg1, arg2)
#define AP_PROCESS_REQUEST_RETURN_ENABLED() (0)
#define AP_PROCESS_CONNECTION_DISPATCH_COMPLETE(arg0, arg1)
#define AP_PROCESS_CONNECTION_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_PROCESS_CONNECTION_DISPATCH_INVOKE(arg0)
#define AP_PROCESS_CONNECTION_DISPATCH_INVOKE_ENABLED() (0)
#define AP_PROCESS_CONNECTION_ENTRY()
#define AP_PROCESS_CONNECTION_ENTRY_ENABLED() (0)
#define AP_PROCESS_CONNECTION_RETURN(arg0)
#define AP_PROCESS_CONNECTION_RETURN_ENABLED() (0)
#define AP_PROXY_RUN(arg0, arg1, arg2, arg3, arg4)
#define AP_PROXY_RUN_ENABLED() (0)
#define AP_PROXY_RUN_FINISHED(arg0, arg1, arg2)
#define AP_PROXY_RUN_FINISHED_ENABLED() (0)
#define AP_QUICK_HANDLER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_QUICK_HANDLER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_QUICK_HANDLER_DISPATCH_INVOKE(arg0)
#define AP_QUICK_HANDLER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_QUICK_HANDLER_ENTRY()
#define AP_QUICK_HANDLER_ENTRY_ENABLED() (0)
#define AP_QUICK_HANDLER_RETURN(arg0)
#define AP_QUICK_HANDLER_RETURN_ENABLED() (0)
#define AP_READ_REQUEST_ENTRY(arg0, arg1)
#define AP_READ_REQUEST_ENTRY_ENABLED() (0)
#define AP_READ_REQUEST_FAILURE(arg0)
#define AP_READ_REQUEST_FAILURE_ENABLED() (0)
#define AP_READ_REQUEST_SUCCESS(arg0, arg1, arg2, arg3, arg4)
#define AP_READ_REQUEST_SUCCESS_ENABLED() (0)
#define AP_REWRITE_LOG(arg0, arg1, arg2, arg3, arg4)
#define AP_REWRITE_LOG_ENABLED() (0)
#define AP_SCHEME_HANDLER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_SCHEME_HANDLER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_SCHEME_HANDLER_DISPATCH_INVOKE(arg0)
#define AP_SCHEME_HANDLER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_SCHEME_HANDLER_ENTRY()
#define AP_SCHEME_HANDLER_ENTRY_ENABLED() (0)
#define AP_SCHEME_HANDLER_RETURN(arg0)
#define AP_SCHEME_HANDLER_RETURN_ENABLED() (0)
#define AP_TEST_CONFIG_DISPATCH_COMPLETE(arg0, arg1)
#define AP_TEST_CONFIG_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_TEST_CONFIG_DISPATCH_INVOKE(arg0)
#define AP_TEST_CONFIG_DISPATCH_INVOKE_ENABLED() (0)
#define AP_TEST_CONFIG_ENTRY()
#define AP_TEST_CONFIG_ENTRY_ENABLED() (0)
#define AP_TEST_CONFIG_RETURN(arg0)
#define AP_TEST_CONFIG_RETURN_ENABLED() (0)
#define AP_TRANSLATE_NAME_DISPATCH_COMPLETE(arg0, arg1)
#define AP_TRANSLATE_NAME_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_TRANSLATE_NAME_DISPATCH_INVOKE(arg0)
#define AP_TRANSLATE_NAME_DISPATCH_INVOKE_ENABLED() (0)
#define AP_TRANSLATE_NAME_ENTRY()
#define AP_TRANSLATE_NAME_ENTRY_ENABLED() (0)
#define AP_TRANSLATE_NAME_RETURN(arg0)
#define AP_TRANSLATE_NAME_RETURN_ENABLED() (0)
#define AP_TYPE_CHECKER_DISPATCH_COMPLETE(arg0, arg1)
#define AP_TYPE_CHECKER_DISPATCH_COMPLETE_ENABLED() (0)
#define AP_TYPE_CHECKER_DISPATCH_INVOKE(arg0)
#define AP_TYPE_CHECKER_DISPATCH_INVOKE_ENABLED() (0)
#define AP_TYPE_CHECKER_ENTRY()
#define AP_TYPE_CHECKER_ENTRY_ENABLED() (0)
#define AP_TYPE_CHECKER_RETURN(arg0)
#define AP_TYPE_CHECKER_RETURN_ENABLED() (0)

#endif

