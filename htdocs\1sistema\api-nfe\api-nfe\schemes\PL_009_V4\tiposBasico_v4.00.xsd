<?xml version="1.0" encoding="UTF-8"?>
<!-- PL_008  - 30/07/2013- NT 2013/005 -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:nfe="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:simpleType name="TCodUfIBGE">
		<xs:annotation>
			<xs:documentation>Tipo Código da UF da tabela do IBGE</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="11"/>
			<xs:enumeration value="12"/>
			<xs:enumeration value="13"/>
			<xs:enumeration value="14"/>
			<xs:enumeration value="15"/>
			<xs:enumeration value="16"/>
			<xs:enumeration value="17"/>
			<xs:enumeration value="21"/>
			<xs:enumeration value="22"/>
			<xs:enumeration value="23"/>
			<xs:enumeration value="24"/>
			<xs:enumeration value="25"/>
			<xs:enumeration value="26"/>
			<xs:enumeration value="27"/>
			<xs:enumeration value="28"/>
			<xs:enumeration value="29"/>
			<xs:enumeration value="31"/>
			<xs:enumeration value="32"/>
			<xs:enumeration value="33"/>
			<xs:enumeration value="35"/>
			<xs:enumeration value="41"/>
			<xs:enumeration value="42"/>
			<xs:enumeration value="43"/>
			<xs:enumeration value="50"/>
			<xs:enumeration value="51"/>
			<xs:enumeration value="52"/>
			<xs:enumeration value="53"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCodMunIBGE">
		<xs:annotation>
			<xs:documentation>Tipo Código do Município da tabela do IBGE</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{7}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TChNFe">
		<xs:annotation>
			<xs:documentation>Tipo Chave da Nota Fiscal Eletrônica</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="44"/>
			<xs:pattern value="[0-9]{44}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TProt">
		<xs:annotation>
			<xs:documentation>Tipo Número do Protocolo de Status</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="15"/>
			<xs:pattern value="[0-9]{15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TRec">
		<xs:annotation>
			<xs:documentation>Tipo Número do Recibo do envio de lote de NF-e</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="15"/>
			<xs:pattern value="[0-9]{15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TStat">
		<xs:annotation>
			<xs:documentation>Tipo Código da Mensagem enviada</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="3"/>
			<xs:pattern value="[0-9]{3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCnpj">
		<xs:annotation>
			<xs:documentation>Tipo Número do CNPJ</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="[0-9]{14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCnpjVar">
		<xs:annotation>
			<xs:documentation>Tipo Número do CNPJ tmanho varíavel (3-14)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="[0-9]{3,14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCnpjOpc">
		<xs:annotation>
			<xs:documentation>Tipo Número do CNPJ Opcional</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="[0-9]{0}|[0-9]{14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCpf">
		<xs:annotation>
			<xs:documentation>Tipo Número do CPF</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="11"/>
			<xs:pattern value="[0-9]{11}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCpfVar">
		<xs:annotation>
			<xs:documentation>Tipo Número do CPF de tamanho variável (3-11)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="11"/>
			<xs:pattern value="[0-9]{3,11}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0104v">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com até 1 dígitos inteiros, podendo ter de 1 até 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{1,4}|[1-9]{1}(\.[0-9]{1,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0204v">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com até 2 dígitos inteiros, podendo ter de 1 até 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{1,4}|[1-9]{1}[0-9]{0,1}(\.[0-9]{1,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0302a04">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com até 3 dígitos inteiros, podendo ter de 2 até 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{2,4}|[1-9]{1}[0-9]{0,2}(\.[0-9]{2,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0302a04Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com até 3 dígitos inteiros e 2 até 4 decimais. Utilizados em TAGs opcionais, não aceita valor zero.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[0-9]{2,4}|[1-9]{1}[0-9]{0,2}(\.[0-9]{2,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0302Max100">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 3 inteiros (no máximo 100), com 2 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0(\.[0-9]{2})?|100(\.00)?|[1-9]{1}[0-9]{0,1}(\.[0-9]{2})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0304Max100">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 3 inteiros (no máximo 100), com 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0(\.[0-9]{4})?|100(\.00)?|[1-9]{1}[0-9]{0,1}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_03v00a04Max100Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 3 inteiros (no máximo 100), com 4 decimais, não aceita valor zero</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0(\.[1-9][0-9]{0,3})|0(\.[0][1-9][0-9]{0,2})|0(\.[0][0][1-9][0-9]{0,1})|0(\.[0][0][0][1-9])|100(\.[0]{1,4})?|[1-9]{1}[0-9]{0,1}(\.[0-9]{1,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0302a04Max100">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 3 inteiros (no máximo 100), com até 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0(\.[0-9]{2,4})?|[1-9]{1}[0-9]{0,1}(\.[0-9]{2,4})?|100(\.0{2,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0803v">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 8 inteiros, podendo ter de 1 até 3 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{3}|[1-9]{1}[0-9]{0,7}(\.[0-9]{1,3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1104">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 11 inteiros, podendo ter 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{4}|[1-9]{1}[0-9]{0,10}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1104v">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 11 inteiros, podendo ter de 1 até 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{1,4}|[1-9]{1}[0-9]{0,10}|[1-9]{1}[0-9]{0,10}(\.[0-9]{1,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1104Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 11 inteiros, podendo ter 4 decimais (utilizado em tags opcionais)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[1-9]{1}[0-9]{3}|0\.[0-9]{3}[1-9]{1}|0\.[0-9]{2}[1-9]{1}[0-9]{1}|0\.[0-9]{1}[1-9]{1}[0-9]{2}|[1-9]{1}[0-9]{0,10}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1110v">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 11 inteiros, podendo ter de 1 até 10 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{1,10}|[1-9]{1}[0-9]{0,10}|[1-9]{1}[0-9]{0,10}(\.[0-9]{1,10})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1203">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 12 inteiros, podendo ter  3 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{3}|[1-9]{1}[0-9]{0,11}(\.[0-9]{3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1204">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 12 inteiros e 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{1,4}|[1-9]{1}[0-9]{0,11}|[1-9]{1}[0-9]{0,11}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1204v">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 12 inteiros de 1 até 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{1,4}|[1-9]{1}[0-9]{0,11}|[1-9]{1}[0-9]{0,11}(\.[0-9]{1,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1204Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 12 inteiros com 1 até 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[0-9]{1,4}|[1-9]{1}[0-9]{0,11}|[1-9]{1}[0-9]{0,11}(\.[0-9]{1,4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1204temperatura">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 12 inteiros, 1 a 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[1-9]{1}[0-9]{3}|0\.[0-9]{3}[1-9]{1}|0\.[0-9]{2}[1-9]{1}[0-9]{1}|0\.[0-9]{1}[1-9]{1}[0-9]{2}|[1-9]{1}[0-9]{0,11}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1302">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 13 de corpo e 2 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{2}|[1-9]{1}[0-9]{0,12}(\.[0-9]{2})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1302Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 13 de corpo e 2 decimais, utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[0-9]{1}[1-9]{1}|0\.[1-9]{1}[0-9]{1}|[1-9]{1}[0-9]{0,12}(\.[0-9]{2})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIeDest">
		<xs:annotation>
			<xs:documentation>Tipo Inscrição Estadual do Destinatário // alterado para aceitar vazio ou ISENTO - maio/2010 v2.0</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="ISENTO|[0-9]{2,14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIeDestNaoIsento">
		<xs:annotation>
			<xs:documentation>Tipo Inscrição Estadual do Destinatário // alterado para aceitar vazio ou ISENTO - maio/2010 v2.0</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="[0-9]{2,14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIeST">
		<xs:annotation>
			<xs:documentation>Tipo Inscrição Estadual do ST // acrescentado EM 24/10/08</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="[0-9]{2,14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIe">
		<xs:annotation>
			<xs:documentation>Tipo Inscrição Estadual do Emitente // alterado EM 24/10/08 para aceitar ISENTO</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="[0-9]{2,14}|ISENTO"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TMod">
		<xs:annotation>
			<xs:documentation>Tipo Modelo Documento Fiscal</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="55"/>
			<xs:enumeration value="65"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TNF">
		<xs:annotation>
			<xs:documentation>Tipo Número do Documento Fiscal</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[1-9]{1}[0-9]{0,8}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TSerie">
		<xs:annotation>
			<xs:documentation>Tipo Série do Documento Fiscal </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|[1-9]{1}[0-9]{0,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TUf">
		<xs:annotation>
			<xs:documentation>Tipo Sigla da UF</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="AC"/>
			<xs:enumeration value="AL"/>
			<xs:enumeration value="AM"/>
			<xs:enumeration value="AP"/>
			<xs:enumeration value="BA"/>
			<xs:enumeration value="CE"/>
			<xs:enumeration value="DF"/>
			<xs:enumeration value="ES"/>
			<xs:enumeration value="GO"/>
			<xs:enumeration value="MA"/>
			<xs:enumeration value="MG"/>
			<xs:enumeration value="MS"/>
			<xs:enumeration value="MT"/>
			<xs:enumeration value="PA"/>
			<xs:enumeration value="PB"/>
			<xs:enumeration value="PE"/>
			<xs:enumeration value="PI"/>
			<xs:enumeration value="PR"/>
			<xs:enumeration value="RJ"/>
			<xs:enumeration value="RN"/>
			<xs:enumeration value="RO"/>
			<xs:enumeration value="RR"/>
			<xs:enumeration value="RS"/>
			<xs:enumeration value="SC"/>
			<xs:enumeration value="SE"/>
			<xs:enumeration value="SP"/>
			<xs:enumeration value="TO"/>
			<xs:enumeration value="EX"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TUfEmi">
		<xs:annotation>
			<xs:documentation>Tipo Sigla da UF de emissor // acrescentado em 24/10/08 </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="AC"/>
			<xs:enumeration value="AL"/>
			<xs:enumeration value="AM"/>
			<xs:enumeration value="AP"/>
			<xs:enumeration value="BA"/>
			<xs:enumeration value="CE"/>
			<xs:enumeration value="DF"/>
			<xs:enumeration value="ES"/>
			<xs:enumeration value="GO"/>
			<xs:enumeration value="MA"/>
			<xs:enumeration value="MG"/>
			<xs:enumeration value="MS"/>
			<xs:enumeration value="MT"/>
			<xs:enumeration value="PA"/>
			<xs:enumeration value="PB"/>
			<xs:enumeration value="PE"/>
			<xs:enumeration value="PI"/>
			<xs:enumeration value="PR"/>
			<xs:enumeration value="RJ"/>
			<xs:enumeration value="RN"/>
			<xs:enumeration value="RO"/>
			<xs:enumeration value="RR"/>
			<xs:enumeration value="RS"/>
			<xs:enumeration value="SC"/>
			<xs:enumeration value="SE"/>
			<xs:enumeration value="SP"/>
			<xs:enumeration value="TO"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TAmb">
		<xs:annotation>
			<xs:documentation>Tipo Ambiente</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TVerAplic">
		<xs:annotation>
			<xs:documentation>Tipo Versão do Aplicativo</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TMotivo">
		<xs:annotation>
			<xs:documentation>Tipo Motivo</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:maxLength value="255"/>
			<xs:minLength value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TJust">
		<xs:annotation>
			<xs:documentation>Tipo Justificativa</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:minLength value="15"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TServ">
		<xs:annotation>
			<xs:documentation>Tipo Serviço solicitado</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString"/>
	</xs:simpleType>
	<xs:simpleType name="Tano">
		<xs:annotation>
			<xs:documentation> Tipo ano</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TMed">
		<xs:annotation>
			<xs:documentation> Tipo temp médio em segundos</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{1,4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TString">
		<xs:annotation>
			<xs:documentation> Tipo string genérico</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[!-ÿ]{1}[ -ÿ]*[!-ÿ]{1}|[!-ÿ]{1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TData">
		<xs:annotation>
			<xs:documentation> Tipo data AAAA-MM-DD</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TTime">
		<xs:annotation>
			<xs:documentation> Tipo hora HH:MM:SS // tipo acrescentado na v2.0</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="(([0-1][0-9])|([2][0-3])):([0-5][0-9]):([0-5][0-9])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDateTimeUTC">
		<xs:annotation>
			<xs:documentation>Data e Hora, formato UTC (AAAA-MM-DDThh:mm:ssTZD, onde TZD = +hh:mm ou -hh:mm)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d([\-,\+](0[0-9]|10|11):00|([\+](12):00))"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TPlaca">
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[A-Z]{2,3}[0-9]{4}|[A-Z]{3,4}[0-9]{3}|[A-Z0-9]{7}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCOrgaoIBGE">
		<xs:annotation>
			<xs:documentation>Tipo Código de orgão (UF da tabela do IBGE + 90 RFB)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="11"/>
			<xs:enumeration value="12"/>
			<xs:enumeration value="13"/>
			<xs:enumeration value="14"/>
			<xs:enumeration value="15"/>
			<xs:enumeration value="16"/>
			<xs:enumeration value="17"/>
			<xs:enumeration value="21"/>
			<xs:enumeration value="22"/>
			<xs:enumeration value="23"/>
			<xs:enumeration value="24"/>
			<xs:enumeration value="25"/>
			<xs:enumeration value="26"/>
			<xs:enumeration value="27"/>
			<xs:enumeration value="28"/>
			<xs:enumeration value="29"/>
			<xs:enumeration value="31"/>
			<xs:enumeration value="32"/>
			<xs:enumeration value="33"/>
			<xs:enumeration value="35"/>
			<xs:enumeration value="41"/>
			<xs:enumeration value="42"/>
			<xs:enumeration value="43"/>
			<xs:enumeration value="50"/>
			<xs:enumeration value="51"/>
			<xs:enumeration value="52"/>
			<xs:enumeration value="53"/>
			<xs:enumeration value="90"/>
			<xs:enumeration value="91"/>
			<xs:enumeration value="92"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
