<?xml version="1.0" encoding="UTF-8"?>
<!--  PL_006f versao com correcoes no xServ para tornar a literal INUTILIZAR obrigatoria 21/05/2010 -->
<!--  PL_006c versao com correcoes 24/12/2009 -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
	<xs:include schemaLocation="tiposBasico_v4.00.xsd"/>
	<xs:complexType name="TInutNFe">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de Inutilização de Numeração da Nota Fiscal Eletrônica</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infInut">
				<xs:annotation>
					<xs:documentation>Dados do Pedido de Inutilização de Numeração da Nota Fiscal Eletrônica</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xServ">
							<xs:annotation>
								<xs:documentation>Serviço Solicitado</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TServ">
									<xs:enumeration value="INUTILIZAR"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="cUF" type="TCodUfIBGE">
							<xs:annotation>
								<xs:documentation>Código da UF do emitente</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ano" type="Tano">
							<xs:annotation>
								<xs:documentation>Ano de inutilização da numeração</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="CNPJ" type="TCnpj">
							<xs:annotation>
								<xs:documentation>CNPJ do emitente</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="mod" type="TMod">
							<xs:annotation>
								<xs:documentation>Modelo da NF-e (55, 65 etc.)</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="serie" type="TSerie">
							<xs:annotation>
								<xs:documentation>Série da NF-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nNFIni" type="TNF">
							<xs:annotation>
								<xs:documentation>Número da NF-e inicial</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nNFFin" type="TNF">
							<xs:annotation>
								<xs:documentation>Número da NF-e final</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xJust" type="TJust">
							<xs:annotation>
								<xs:documentation>Justificativa do pedido de inutilização</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" use="required">
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="ID[0-9]{41}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerInutNFe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetInutNFe">
		<xs:annotation>
			<xs:documentation>Tipo retorno do Pedido de Inutilização de Numeração da Nota Fiscal Eletrônica</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infInut">
				<xs:annotation>
					<xs:documentation>Dados do Retorno do Pedido de Inutilização de Numeração da Nota Fiscal Eletrônica</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="verAplic" type="TVerAplic">
							<xs:annotation>
								<xs:documentation>Versão do Aplicativo que processou a NF-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cStat" type="TStat">
							<xs:annotation>
								<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xMotivo" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cUF" type="TCodUfIBGE">
							<xs:annotation>
								<xs:documentation>Código da UF que atendeu a solicitação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ano" type="Tano" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Ano de inutilização da numeração</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="CNPJ" type="TCnpj" minOccurs="0">
							<xs:annotation>
								<xs:documentation>CNPJ do emitente</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="mod" type="TMod" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Modelo da NF-e (55, etc.)</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="serie" type="TSerie" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Série da NF-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nNFIni" type="TNF" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número da NF-e inicial</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nNFFin" type="TNF" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número da NF-e final</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="dhRecbto" type="TDateTimeUTC">
							<xs:annotation>
								<xs:documentation>Data e hora de recebimento, no formato AAAA-MM-DDTHH:MM:SS. Deve ser preenchida com data e hora da gravação no Banco em caso de Confirmação. Em caso de Rejeição, com data e hora do recebimento do Pedido de Inutilização.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nProt" type="TProt" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número do Protocolo de Status da NF-e. 1 posição (1 – Secretaria de Fazenda Estadual 2 – Receita Federal); 2 - código da UF - 2 posições ano; 10 seqüencial no ano.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" type="xs:ID" use="optional"/>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerInutNFe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TProcInutNFe">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de inutilzação de númeração de  NF-e processado</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="inutNFe" type="TInutNFe"/>
			<xs:element name="retInutNFe" type="TRetInutNFe"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerInutNFe" use="required"/>
	</xs:complexType>
	<xs:simpleType name="TVerInutNFe">
		<xs:annotation>
			<xs:documentation>Tipo Versão do leiaute de Inutilização 4.00</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:token">
			<xs:pattern value="4\.00"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
