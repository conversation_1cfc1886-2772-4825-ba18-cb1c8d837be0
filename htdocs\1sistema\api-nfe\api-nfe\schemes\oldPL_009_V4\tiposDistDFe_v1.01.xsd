<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
  <xs:simpleType name="TNSU">
    <xs:annotation>
      <xs:documentation>Tipo número sequencial único</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:token">
      <xs:pattern value="[0-9]{15}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TQNSU">
    <xs:annotation>
      <xs:documentation>Tipo quantidade de NSU</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:unsignedShort">
      <xs:minInclusive value="1"/>
      <xs:maxInclusive value="50"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TVerDistDFe">
    <xs:annotation>
      <xs:documentation>Tipo Versão dos leiautes do Web Service NFeDistribuicaoDFe</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:enumeration value="1.01"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TAmb">
    <xs:annotation>
      <xs:documentation>Tipo Ambiente</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:enumeration value="1"/>
      <xs:enumeration value="2"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TCodUfIBGE">
    <xs:annotation>
      <xs:documentation>Tipo Código da UF da tabela do IBGE</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:enumeration value="11"/>
      <xs:enumeration value="12"/>
      <xs:enumeration value="13"/>
      <xs:enumeration value="14"/>
      <xs:enumeration value="15"/>
      <xs:enumeration value="16"/>
      <xs:enumeration value="17"/>
      <xs:enumeration value="21"/>
      <xs:enumeration value="22"/>
      <xs:enumeration value="23"/>
      <xs:enumeration value="24"/>
      <xs:enumeration value="25"/>
      <xs:enumeration value="26"/>
      <xs:enumeration value="27"/>
      <xs:enumeration value="28"/>
      <xs:enumeration value="29"/>
      <xs:enumeration value="31"/>
      <xs:enumeration value="32"/>
      <xs:enumeration value="33"/>
      <xs:enumeration value="35"/>
      <xs:enumeration value="41"/>
      <xs:enumeration value="42"/>
      <xs:enumeration value="43"/>
      <xs:enumeration value="50"/>
      <xs:enumeration value="51"/>
      <xs:enumeration value="52"/>
      <xs:enumeration value="53"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TCOrgaoIBGE">
    <xs:annotation>
      <xs:documentation>Tipo Código de orgão (UF da tabela do IBGE + 90 RFB)</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:enumeration value="11"/>
      <xs:enumeration value="12"/>
      <xs:enumeration value="13"/>
      <xs:enumeration value="14"/>
      <xs:enumeration value="15"/>
      <xs:enumeration value="16"/>
      <xs:enumeration value="17"/>
      <xs:enumeration value="21"/>
      <xs:enumeration value="22"/>
      <xs:enumeration value="23"/>
      <xs:enumeration value="24"/>
      <xs:enumeration value="25"/>
      <xs:enumeration value="26"/>
      <xs:enumeration value="27"/>
      <xs:enumeration value="28"/>
      <xs:enumeration value="29"/>
      <xs:enumeration value="31"/>
      <xs:enumeration value="32"/>
      <xs:enumeration value="33"/>
      <xs:enumeration value="35"/>
      <xs:enumeration value="41"/>
      <xs:enumeration value="42"/>
      <xs:enumeration value="43"/>
      <xs:enumeration value="50"/>
      <xs:enumeration value="51"/>
      <xs:enumeration value="52"/>
      <xs:enumeration value="53"/>
      <xs:enumeration value="90"/>
      <xs:enumeration value="91"/>
      <xs:enumeration value="92"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TCnpj">
    <xs:annotation>
      <xs:documentation>Tipo Número do CNPJ</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:maxLength value="14"/>
      <xs:pattern value="[0-9]{14}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TCpf">
    <xs:annotation>
      <xs:documentation>Tipo Número do CPF</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:maxLength value="11"/>
      <xs:pattern value="[0-9]{11}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TVerAplic">
    <xs:annotation>
      <xs:documentation>Tipo Versão do Aplicativo</xs:documentation>
    </xs:annotation>
    <xs:restriction base="TString">
      <xs:minLength value="1"/>
      <xs:maxLength value="20"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TStat">
    <xs:annotation>
      <xs:documentation>Tipo Código da Mensagem enviada</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:maxLength value="3"/>
      <xs:pattern value="[0-9]{3}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TMotivo">
    <xs:annotation>
      <xs:documentation>Tipo Motivo</xs:documentation>
    </xs:annotation>
    <xs:restriction base="TString">
      <xs:maxLength value="255"/>
      <xs:minLength value="1"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TString">
    <xs:annotation>
      <xs:documentation> Tipo string genérico</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:pattern value="[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TChNFe">
    <xs:annotation>
      <xs:documentation>Tipo Chave da Nota Fiscal Eletrônica</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:maxLength value="44"/>
      <xs:pattern value="[0-9]{44}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TProt">
    <xs:annotation>
      <xs:documentation>Tipo Número do Protocolo de Status</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:maxLength value="15"/>
      <xs:pattern value="[0-9]{15}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TDateTimeUTC">
    <xs:annotation>
      <xs:documentation>Data e Hora, formato UTC (AAAA-MM-DDThh:mm:ssTZD, onde TZD = +hh:mm ou -hh:mm)</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d([\-,\+](0[0-9]|10|11):00|([\+](12):00))"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TIe">
    <xs:annotation>
      <xs:documentation>Tipo Inscrição Estadual do Emitente // alterado EM 24/10/08 para aceitar ISENTO</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:maxLength value="14"/>
      <xs:pattern value="[0-9]{2,14}|ISENTO"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TDec_1302">
    <xs:annotation>
      <xs:documentation>Tipo Decimal com 15 dígitos, sendo 13 de corpo e 2 decimais</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:pattern value="0|0\.[0-9]{2}|[1-9]{1}[0-9]{0,12}(\.[0-9]{2})?"/>
    </xs:restriction>
  </xs:simpleType>
</xs:schema>

