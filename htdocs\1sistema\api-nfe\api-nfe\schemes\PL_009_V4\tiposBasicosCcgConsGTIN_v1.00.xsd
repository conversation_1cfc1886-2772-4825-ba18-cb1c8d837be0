<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:nfe="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:simpleType name="TString">
		<xs:annotation>
			<xs:documentation>Tipo string genérico</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TStat">
		<xs:annotation>
			<xs:documentation>Tipo Código do Status da resposta</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:pattern value="[0-9]{3,4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TVerAplic">
		<xs:annotation>
			<xs:documentation>Tipo Versão do Aplicativo</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TMotivo">
		<xs:annotation>
			<xs:documentation>Tipo Motivo (descrição do status da resposta)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TData">
		<xs:annotation>
			<xs:documentation> Tipo data AAAA-MM-DD</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:pattern value="((((20|19|18)(([02468][048])|([13579][26]))-02-29))|((20|19|18)[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))"/>
			<!-- Alterado para permitir datas anteriores ao ano 2000 -->
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDateTimeUTC">
		<xs:annotation>
			<xs:documentation>Data e Hora, formato UTC (AAAA-MM-DDThh:mm:ssTZD, onde TZD = +hh:mm ou -hh:mm)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d([\-,\+](0[0-9]|10|11):00|([\+](12):00))"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCodGTIN">
		<xs:annotation>
			<xs:documentation>Tipo Código GTIN</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:pattern value="[0-9]{6,14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TTpGTIN">
		<xs:annotation>
			<xs:documentation>Tipo do GTIN (quantidade de dígitos do GTIN)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:enumeration value="8"/>
			<xs:enumeration value="12"/>
			<xs:enumeration value="13"/>
			<xs:enumeration value="14"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TSitGTIN">
		<xs:annotation>
			<xs:documentation> Situação do GTIN pela GS1: 0="Não habilitado pela GS1 para uso na NF-e"; 1="Habilitado pela GS1 para uso na NF-e"; 9="Exclusão lógica do GTIN".</xs:documentation>
		</xs:annotation>
		<xs:restriction base="nfe:TString">
			<xs:enumeration value="0"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="9"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
