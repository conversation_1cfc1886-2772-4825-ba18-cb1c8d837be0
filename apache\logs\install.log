Installing Apache HTTP Server 2.x with
 DomainName    = example.com
 ServerName    = www.example.com
 ServerAdmin   = <EMAIL>
 ServerPort    = 80
 ServerSslPort = 443
 ServerRoot    = c:/Apache24
Rewrote docs/conf/extra/httpd-autoindex.conf.in
 to c:/Apache24/conf/original/extra/httpd-autoindex.conf
Rewrote docs/conf/extra/httpd-default.conf.in
 to c:/Apache24/conf/original/extra/httpd-default.conf
Rewrote docs/conf/extra/httpd-ssl.conf.in
 to c:/Apache24/conf/original/extra/httpd-ssl.conf
Rewrote docs/conf/extra/httpd-multilang-errordoc.conf.in
 to c:/Apache24/conf/original/extra/httpd-multilang-errordoc.conf
Rewrote docs/conf/extra/httpd-info.conf.in
 to c:/Apache24/conf/original/extra/httpd-info.conf
Rewrote docs/conf/extra/httpd-userdir.conf.in
 to c:/Apache24/conf/original/extra/httpd-userdir.conf
Rewrote docs/conf/extra/httpd-mpm.conf.in
 to c:/Apache24/conf/original/extra/httpd-mpm.conf
Rewrote docs/conf/httpd.conf.in
 to c:/Apache24/conf/original/httpd.conf
Rewrote docs/conf/extra/proxy-html.conf.in
 to c:/Apache24/conf/original/extra/proxy-html.conf
Rewrote docs/conf/extra/httpd-vhosts.conf.in
 to c:/Apache24/conf/original/extra/httpd-vhosts.conf
Rewrote docs/conf/extra/httpd-dav.conf.in
 to c:/Apache24/conf/original/extra/httpd-dav.conf
Rewrote docs/conf/extra/httpd-languages.conf.in
 to c:/Apache24/conf/original/extra/httpd-languages.conf
Rewrote docs/conf/extra/httpd-manual.conf.in
 to c:/Apache24/conf/original/extra/httpd-manual.conf
Duplicated c:/Apache24/conf/original/extra/httpd-autoindex.conf
 to c:/Apache24/conf/extra/httpd-autoindex.conf
Duplicated c:/Apache24/conf/original/extra/httpd-default.conf
 to c:/Apache24/conf/extra/httpd-default.conf
Duplicated c:/Apache24/conf/original/extra/httpd-ssl.conf
 to c:/Apache24/conf/extra/httpd-ssl.conf
Duplicated c:/Apache24/conf/original/extra/httpd-multilang-errordoc.conf
 to c:/Apache24/conf/extra/httpd-multilang-errordoc.conf
Duplicated c:/Apache24/conf/original/extra/httpd-info.conf
 to c:/Apache24/conf/extra/httpd-info.conf
Duplicated c:/Apache24/conf/original/extra/httpd-userdir.conf
 to c:/Apache24/conf/extra/httpd-userdir.conf
Duplicated c:/Apache24/conf/original/extra/httpd-mpm.conf
 to c:/Apache24/conf/extra/httpd-mpm.conf
Duplicated c:/Apache24/conf/original/httpd.conf
 to c:/Apache24/conf/httpd.conf
Duplicated c:/Apache24/conf/original/magic
 to c:/Apache24/conf/magic
Duplicated c:/Apache24/conf/original/charset.conv
 to c:/Apache24/conf/charset.conv
Duplicated c:/Apache24/conf/original/extra/proxy-html.conf
 to c:/Apache24/conf/extra/proxy-html.conf
Duplicated c:/Apache24/conf/original/extra/httpd-vhosts.conf
 to c:/Apache24/conf/extra/httpd-vhosts.conf
Duplicated c:/Apache24/conf/original/extra/httpd-dav.conf
 to c:/Apache24/conf/extra/httpd-dav.conf
Duplicated c:/Apache24/conf/original/mime.types
 to c:/Apache24/conf/mime.types
Duplicated c:/Apache24/conf/original/extra/httpd-languages.conf
 to c:/Apache24/conf/extra/httpd-languages.conf
Duplicated c:/Apache24/conf/original/extra/httpd-manual.conf
 to c:/Apache24/conf/extra/httpd-manual.conf
