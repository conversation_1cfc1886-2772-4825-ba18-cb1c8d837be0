Public Domain Icons

     These icons were originally made for Mosaic for X and have been
     included in the NCSA httpd and Apache server distributions in the
     past. They are in the public domain and may be freely included in any
     application. The originals were done by <PERSON> (kev<PERSON><PERSON>@kevcom.com).
     <PERSON> tuned the icon colors and added a few new images.

     If you'd like to contribute additions to this set, contact the httpd
     documentation project <http://httpd.apache.org/docs-project/>.

     Almost all of these icons are 20x22 pixels in size.  There are
     alternative icons in the "small" directory that are 16x16 in size,
     provided by <PERSON> (<EMAIL>).

Suggested Uses

The following are a few suggestions, to serve as a starting point for ideas.
Please feel free to tweak and rename the icons as you like.

     a.gif
          This might be used to represent PostScript or text layout
          languages.

     alert.black.gif, alert.red.gif
          These can be used to highlight any important items, such as a
          README file in a directory.

     back.gif, forward.gif
          These can be used as links to go to previous and next areas.

     ball.gray.gif, ball.red.gif
          These might be used as bullets.

     binary.gif
          This can be used to represent binary files.

     binhex.gif
          This can represent BinHex-encoded data.

     blank.gif
          This can be used as a placeholder or a spacing element.

     bomb.gif
          This can be used to represent core files.

     box1.gif, box2.gif
          These icons can be used to represent generic 3D applications and
          related files.

     broken.gif
          This can represent corrupted data.

     burst.gif
          This can call attention to new and important items.

     c.gif
          This might represent C source code.

     comp.blue.gif, comp.gray.gif
          These little computer icons can stand for telnet or FTP
          sessions.

     compressed.gif
          This may represent compressed data.

     continued.gif
          This can be a link to a continued listing of a directory.

     down.gif, up.gif, left.gif, right.gif
          These can be used to scroll up, down, left and right in a
          listing or may be used to denote items in an outline.

     dir.gif
          Identical to folder.gif below.

     diskimg.gif
          This can represent floppy disk storage.

     dvi.gif
          This can represent DVI files.

     f.gif
          This might represent FORTRAN or Forth source code.

     folder.gif, folder.open.gif, folder.sec.gif
          The folder can represent directories. There is also a version
          that can represent secure directories or directories that cannot
          be viewed.

     generic.gif, generic.sec.gif, generic.red.gif
          These can represent generic files, secure files, and important
          files, respectively.

     hand.right.gif, hand.up.gif
          These can point out important items (pun intended).

     image1.gif, image2.gif, image3.gif
          These can represent image formats of various types.

     index.gif
          This might represent a WAIS index or search facility.

     layout.gif
          This might represent files and formats that contain graphics as
          well as text layout, such as HTML and PDF files.

     link.gif
          This might represent files that are symbolic links.

     movie.gif
          This can represent various movie formats.

     p.gif
          This may stand for Perl or Python source code.

     pie0.gif ... pie8.gif
          These icons can be used in applications where a list of
          documents is returned from a search. The little pie chart images
          can denote how relevant the documents may be to your search
          query.

     patch.gif
          This may stand for patches and diff files.

     portal.gif
          This might be a link to an online service or a 3D world.

     pdf.gif, ps.gif, quill.gif
          These may represent PDF and PostScript files.

     screw1.gif, screw2.gif
          These may represent CAD or engineering data and formats.

     script.gif
          This can represent any of various interpreted languages, such as
          Perl, python, TCL, and shell scripts, as well as server
          configuration files.

     sound1.gif, sound2.gif
          These can represent sound files.

     sphere1.gif, sphere2.gif
          These can represent 3D worlds or rendering applications and
          formats.

     tar.gif
          This can represent TAR archive files.

     tex.gif
          This can represent TeX files.

     text.gif
          This can represent generic (plain) text files.

     transfer.gif
          This can represent FTP transfers or uploads/downloads.

     unknown.gif
          This may represent a file of an unknown type.

     uu.gif, uuencoded.gif
          This can stand for uuencoded data.

     world1.gif, world2.gif
          These can represent 3D worlds or other 3D formats.
