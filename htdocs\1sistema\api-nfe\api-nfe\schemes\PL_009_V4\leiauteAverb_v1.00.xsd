<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
  <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
  <xs:include schemaLocation="tiposBasico_v1.03.xsd"/>
  <xs:complexType name="TEvento">
    <xs:annotation>
      <xs:documentation>Tipo Evento</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="infEvento">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="cOrgao" >
              <xs:annotation>
                <xs:documentation>Código do órgão de recepção do Evento. Para o evento de averbação de exportação será utilizado o valor 91 para o Ambiente Nacional.</xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:whiteSpace value="preserve"/>
                  <xs:enumeration value="91" />
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb" type="TAmb">
              <xs:annotation>
                <xs:documentation>
                  Identificação do Ambiente:
                  1 - Produção
                  2 - Homologação
                </xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="CNPJ" type="TCnpj">
              <xs:annotation>
                <xs:documentation>CNPJ do  autor do evento</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="chNFe" type="TChNFe">
              <xs:annotation>
                <xs:documentation>Chave de Acesso da NF-e vinculada ao evento</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="dhEvento" type="TDateTimeUTC">
              <xs:annotation>
                <xs:documentation>Data de emissão no formato UTC.  AAAA-MM-DDThh:mm:ssTZD</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="tpEvento">
              <xs:annotation>
                <xs:documentation>Tipo do Evento</xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:whiteSpace value="preserve"/>
                  <xs:enumeration value="790700"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="nSeqEvento">
              <xs:annotation>
                <xs:documentation>
                  Seqüencial do evento para o mesmo tipo de evento. Para maioria dos eventos será 1, nos casos em que possa existir mais de um evento, como é o caso dos eventos de averbação, o autor do evento deve numerar de forma seqüencial
                </xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:whiteSpace value="preserve"/>
                  <xs:pattern value="[1-9][0-9]{0,1}"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="verEvento">
              <xs:annotation>
                <xs:documentation>Versão do Tipo do Evento</xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:whiteSpace value="preserve"/>
                  <xs:enumeration value="1.00"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="detEvento">
              <xs:annotation>
                <xs:documentation>Schema XML de validação do evento de averbação da NFe (e790700)</xs:documentation>
              </xs:annotation>
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="descEvento">
                    <xs:annotation>
                      <xs:documentation>Descrição do Evento - “Averbação para Exportação”</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                      <xs:restriction base="xs:string">
                        <xs:whiteSpace value="preserve"/>
                        <xs:enumeration value="Averbação para Exportação"/>
                        <xs:enumeration value="Averbacao para Exportacao"/>
                      </xs:restriction>
                    </xs:simpleType>
                  </xs:element>
                  <xs:element name="tpAutor">
                    <xs:annotation>
                      <xs:documentation>Tipo do Autor do Evento (6=RFB)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                      <xs:restriction base="xs:string">
                        <xs:whiteSpace value="preserve"/>
                        <xs:enumeration value="6"/>
                      </xs:restriction>
                    </xs:simpleType>
                  </xs:element>
                  <xs:element name="verAplic" type="TVerAplic">
                    <xs:annotation>
                      <xs:documentation>Versão do aplicativo do Autor do evento</xs:documentation>
                    </xs:annotation>
                  </xs:element>
                  <xs:element name="itensAverbados"  minOccurs="1" maxOccurs="990" >
                    <xs:annotation>
                      <xs:documentation>Informações dos itens da NF-e do evento.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                      <xs:sequence>
                        <xs:element name="dhEmbarque" type="TDateTimeUTC" >
                          <xs:annotation>
                            <xs:documentation>Data do Embarque no formato AAAA-MM-DDThh:mm:ssTZD</xs:documentation>
                          </xs:annotation>
                        </xs:element>
                        <xs:element name="dhAverbacao" type="TDateTimeUTC" >
                          <xs:annotation>
                            <xs:documentation>Data da averbação no formato AAAA-MM-DDThh:mm:ssTZD</xs:documentation>
                          </xs:annotation>
                        </xs:element>
                        <xs:element name="nDue" >
                          <xs:annotation>
                            <xs:documentation>Número Identificador da Declaração Única do Comércio Exterior associada</xs:documentation>
                          </xs:annotation>
                          <xs:simpleType>
                            <xs:restriction base="xs:string">
                              <xs:whiteSpace value="preserve"/>
                              <xs:pattern value="[0-9]{2}BR[0-9]{10}"/>
                            </xs:restriction>
                          </xs:simpleType>
                        </xs:element>
                        <xs:element name="nItem" >
                          <xs:annotation>
                            <xs:documentation>Número do item da NF-e averbada</xs:documentation>
                          </xs:annotation>
                          <xs:simpleType>
                            <xs:restriction base="xs:string">
                              <xs:whiteSpace value="preserve"/>
                              <xs:pattern value="[0-9]{1,3}"/>
                            </xs:restriction>
                          </xs:simpleType>
                        </xs:element>
                        <xs:element name="nItemDue" >
                          <xs:annotation>
                            <xs:documentation>Informação do número do item na Declaração de Exportação associada a averbação.</xs:documentation>
                          </xs:annotation>
                          <xs:simpleType>
                            <xs:restriction base="xs:string">
                              <xs:whiteSpace value="preserve"/>
                              <xs:pattern value="[0-9]{1,4}"/>
                            </xs:restriction>
                          </xs:simpleType>
                        </xs:element>
                        <xs:element name="qItem" type="TDec_1104Neg">
                          <xs:annotation>
                            <xs:documentation>Quantidade averbada do item na unidade tributária (campo uTrib)</xs:documentation>
                          </xs:annotation>
                        </xs:element>
                        <xs:element name="motAlteracao" minOccurs="0">
                          <xs:annotation>
                            <xs:documentation>
                              Motivo da Alteração
                              1 - Exportação Averbada;
                              2 - Retificação da Quantidade Averbada;
                              3 - Cancelamento da Exportação;
                            </xs:documentation>
                          </xs:annotation>
                          <xs:simpleType>
                            <xs:restriction base="xs:string">
                              <xs:whiteSpace value="preserve"/>
                              <xs:enumeration value="1" />
                              <xs:enumeration value="2" />
                              <xs:enumeration value="3" />
                            </xs:restriction>
                          </xs:simpleType>
                        </xs:element>
                      </xs:sequence>
                    </xs:complexType>
                  </xs:element>
                </xs:sequence>
                <xs:attribute name="versao" use="required">
                  <xs:simpleType>
                    <xs:restriction base="xs:string">
                      <xs:whiteSpace value="preserve"/>
                      <xs:enumeration value="1.00"/>
                    </xs:restriction>
                  </xs:simpleType>
                </xs:attribute>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attribute name="Id" use="required">
            <xs:annotation>
              <xs:documentation>
                Identificador da TAG a ser assinada, a regra de formação do Id é:
                “ID” + tpEvento +  chave da NF-e + nSeqEvento
              </xs:documentation>
            </xs:annotation>
            <xs:simpleType>
              <xs:restriction base="xs:ID">
                <xs:pattern value="ID[0-9]{52}"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
        </xs:complexType>
      </xs:element>
      <xs:element ref="ds:Signature"/>
    </xs:sequence>
    <xs:attribute name="versao" type="TVerEvento" use="required"/>
  </xs:complexType>
  <xs:complexType name="TretEvento">
    <xs:annotation>
      <xs:documentation>Tipo retorno do Evento</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="infEvento">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="tpAmb" type="TAmb">
              <xs:annotation>
                <xs:documentation>
                  Identificação do Ambiente:
                  1 - Produção
                  2 - Homologação
                </xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="verAplic" type="TVerAplic">
              <xs:annotation>
                <xs:documentation>Versão do Aplicativo que recebeu o Evento</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="cOrgao" type="TCOrgaoIBGE">
              <xs:annotation>
                <xs:documentation>Código do órgão de recepção do Evento. Utilizar a Tabela do IBGE extendida, utilizar 91 para identificar o Ambiente Nacional</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="cStat" type="TStat">
              <xs:annotation>
                <xs:documentation>Código do status da registro do Evento</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="xMotivo" type="TMotivo">
              <xs:annotation>
                <xs:documentation>Descrição literal do status do registro do Evento</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="chNFe" type="TChNFe" minOccurs="0">
              <xs:annotation>
                <xs:documentation>Chave de Acesso NF-e vinculada</xs:documentation>
              </xs:annotation>
            </xs:element>
            <xs:element name="tpEvento" minOccurs="0">
              <xs:annotation>
                <xs:documentation>Tipo do Evento vinculado</xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:whiteSpace value="preserve"/>
                  <xs:pattern value="[0-9]{6}"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="xEvento" minOccurs="0">
              <xs:annotation>
                <xs:documentation>Descrição do Evento</xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="TString">
                  <xs:minLength value="5"/>
                  <xs:maxLength value="60"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="nSeqEvento" minOccurs="0">
              <xs:annotation>
                <xs:documentation>Seqüencial do evento</xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:whiteSpace value="preserve"/>
                  <xs:pattern value="[1-9][0-9]{0,1}"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:choice minOccurs="0">
              <xs:annotation>
                <xs:documentation>Identificação do  destinatário da NF-e</xs:documentation>
              </xs:annotation>
              <xs:element name="CNPJDest" type="TCnpjOpc">
                <xs:annotation>
                  <xs:documentation>CNPJ Destinatário</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element name="CPFDest" type="TCpf">
                <xs:annotation>
                  <xs:documentation>CPF Destiantário</xs:documentation>
                </xs:annotation>
              </xs:element>
            </xs:choice>
            <xs:element name="emailDest" minOccurs="0">
              <xs:annotation>
                <xs:documentation>email do destinatário</xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="TString">
                  <xs:minLength value="1"/>
                  <xs:maxLength value="60"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="dhRegEvento">
              <xs:annotation>
                <xs:documentation>Data e Hora de do recebimento do evento ou do registro do evento formato UTC AAAA-MM-DDThh:mm:ssTZD.</xs:documentation>
              </xs:annotation>
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:whiteSpace value="preserve"/>
                  <xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d-0[1-4]:00"/>
                </xs:restriction>
              </xs:simpleType>
            </xs:element>
            <xs:element name="nProt" type="TProt" minOccurs="0">
              <xs:annotation>
                <xs:documentation>Número do protocolo de registro do evento</xs:documentation>
              </xs:annotation>
            </xs:element>
          </xs:sequence>
          <xs:attribute name="Id" use="optional">
            <xs:simpleType>
              <xs:restriction base="xs:ID">
                <xs:pattern value="ID[0-9]{15}"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:attribute>
        </xs:complexType>
      </xs:element>
      <xs:element ref="ds:Signature" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="versao" type="TVerEvento" use="required"/>
  </xs:complexType>
  <xs:complexType name="TEnvEvento">
    <xs:annotation>
      <xs:documentation> Tipo Lote de Envio</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="idLote">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="preserve"/>
            <xs:pattern value="[0-9]{1,15}"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="evento" type="TEvento" maxOccurs="20"/>
    </xs:sequence>
    <xs:attribute name="versao" type="TVerEnvEvento" use="required"/>
  </xs:complexType>
  <xs:complexType name="TRetEnvEvento">
    <xs:annotation>
      <xs:documentation> Tipo Retorno de Lote de Envio</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="idLote">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="preserve"/>
            <xs:pattern value="[0-9]{1,15}"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="tpAmb" type="TAmb">
        <xs:annotation>
          <xs:documentation>
            Identificação do Ambiente:
            1 - Produção
            2 - Homologação
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="verAplic" type="TVerAplic">
        <xs:annotation>
          <xs:documentation>Versão do Aplicativo que recebeu o Evento</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="cOrgao" type="TCOrgaoIBGE">
        <xs:annotation>
          <xs:documentation>Código do òrgao que registrou o Evento</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="cStat" type="TStat">
        <xs:annotation>
          <xs:documentation>Código do status da registro do Evento</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="xMotivo" type="TMotivo">
        <xs:annotation>
          <xs:documentation>Descrição literal do status do registro do Evento</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="retEvento" type="TretEvento" minOccurs="0" maxOccurs="20"/>
    </xs:sequence>
    <xs:attribute name="versao" type="TVerEnvEvento" use="required"/>
  </xs:complexType>
  <xs:complexType name="TProcEvento">
    <xs:annotation>
      <xs:documentation>Tipo procEvento</xs:documentation>
    </xs:annotation>
    <xs:sequence>
      <xs:element name="evento" type="TEvento"/>
      <xs:element name="retEvento" type="TretEvento"/>
    </xs:sequence>
    <xs:attribute name="versao" type="TVerEvento" use="required"/>
  </xs:complexType>
  <xs:simpleType name="TVerEnvEvento">
    <xs:annotation>
      <xs:documentation>Tipo Versão do EnvEvento</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:pattern value="1.00"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TVerEvento">
    <xs:annotation>
      <xs:documentation>Tipo Versão do Evento</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:pattern value="1.00"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TCOrgaoIBGE">
    <xs:annotation>
      <xs:documentation>Tipo Código de orgão (UF da tabela do IBGE)</xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:whiteSpace value="preserve"/>
      <xs:enumeration value="11"/>
      <xs:enumeration value="12"/>
      <xs:enumeration value="13"/>
      <xs:enumeration value="14"/>
      <xs:enumeration value="15"/>
      <xs:enumeration value="16"/>
      <xs:enumeration value="17"/>
      <xs:enumeration value="21"/>
      <xs:enumeration value="22"/>
      <xs:enumeration value="23"/>
      <xs:enumeration value="24"/>
      <xs:enumeration value="25"/>
      <xs:enumeration value="26"/>
      <xs:enumeration value="27"/>
      <xs:enumeration value="28"/>
      <xs:enumeration value="29"/>
      <xs:enumeration value="31"/>
      <xs:enumeration value="32"/>
      <xs:enumeration value="33"/>
      <xs:enumeration value="35"/>
      <xs:enumeration value="41"/>
      <xs:enumeration value="42"/>
      <xs:enumeration value="43"/>
      <xs:enumeration value="50"/>
      <xs:enumeration value="51"/>
      <xs:enumeration value="52"/>
      <xs:enumeration value="53"/>
      <xs:enumeration value="90"/>
      <xs:enumeration value="91"/>
    </xs:restriction>
  </xs:simpleType>
</xs:schema>
