<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<!-- PL_008g  alterações de esquema decorrentes da - NT2015.002  - 15/07/2015 -->
<!-- PL_008h  alterações de esquema decorrentes da - NT2015.003 - 17/09/2015 -->
<!-- PL_008i -->
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
	<xs:include schemaLocation="tiposBasico_v3.10.xsd"/>
	<xs:complexType name="TNFe">
		<xs:annotation>
			<xs:documentation>Tipo Nota Fiscal Eletrônica</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infNFe">
				<xs:annotation>
					<xs:documentation>Informações da Nota Fiscal eletrônica</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ide">
							<xs:annotation>
								<xs:documentation>identificação da NF-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="cUF" type="TCodUfIBGE">
										<xs:annotation>
											<xs:documentation>Código da UF do emitente do Documento Fiscal. Utilizar a Tabela do IBGE.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cNF">
										<xs:annotation>
											<xs:documentation>Código numérico que compõe a Chave de Acesso. Número aleatório gerado pelo emitente para cada NF-e.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{8}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="natOp">
										<xs:annotation>
											<xs:documentation>Descrição da Natureza da Operação</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="indPag">
										<xs:annotation>
											<xs:documentation>Indicador da forma de pagamento:
0 – pagamento à vista;
1 – pagamento à prazo;
2 – outros.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="mod" type="TMod">
										<xs:annotation>
											<xs:documentation>Código do modelo do Documento Fiscal. 55 = NF-e; 65 = NFC-e.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="serie" type="TSerie">
										<xs:annotation>
											<xs:documentation>Série do Documento Fiscal
série normal 0-889
Avulsa Fisco 890-899
SCAN 900-999</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="nNF" type="TNF">
										<xs:annotation>
											<xs:documentation>Número do Documento Fiscal</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="dhEmi" type="TDateTimeUTC">
										<xs:annotation>
											<xs:documentation>Data e Hora de emissão do Documento Fiscal (AAAA-MM-DDThh:mm:ssTZD) ex.: 2012-09-01T13:00:00-03:00</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="dhSaiEnt" type="TDateTimeUTC" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Data e Hora da saída ou de entrada da mercadoria / produto (AAAA-MM-DDTHH:mm:ssTZD)</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpNF">
										<xs:annotation>
											<xs:documentation>Tipo do Documento Fiscal (0 - entrada; 1 - saída)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="idDest">
										<xs:annotation>
											<xs:documentation>Identificador de Local de destino da operação (1-Interna;2-Interestadual;3-Exterior)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cMunFG" type="TCodMunIBGE">
										<xs:annotation>
											<xs:documentation>Código do Município de Ocorrência do Fato Gerador (utilizar a tabela do IBGE)</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpImp">
										<xs:annotation>
											<xs:documentation>Formato de impressão do DANFE (0-sem DANFE;1-DANFe Retrato; 2-DANFe Paisagem;3-DANFe Simplificado;
											4-DANFe NFC-e;5-DANFe NFC-e em mensagem eletrônica)
											</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
												<xs:enumeration value="4"/>
												<xs:enumeration value="5"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpEmis">
										<xs:annotation>
											<xs:documentation>Forma de emissão da NF-e
1 - Normal;
2 - Contingência FS
3 - Contingência SCAN
4 - Contingência DPEC
5 - Contingência FSDA
6 - Contingência SVC - AN
7 - Contingência SVC - RS
9 - Contingência off-line NFC-e</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
												<xs:enumeration value="4"/>
												<xs:enumeration value="5"/>
												<xs:enumeration value="6"/>
												<xs:enumeration value="7"/>
												<xs:enumeration value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cDV">
										<xs:annotation>
											<xs:documentation>Digito Verificador da Chave de Acesso da NF-e</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{1}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpAmb" type="TAmb">
										<xs:annotation>
											<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="finNFe" type="TFinNFe">
										<xs:annotation>
											<xs:documentation>Finalidade da emissão da NF-e:
1 - NFe normal
2 - NFe complementar
3 - NFe de ajuste
4 - Devolução/Retorno</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="indFinal">
										<xs:annotation>
											<xs:documentation>Indica operação com consumidor final (0-Não;1-Consumidor Final)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="indPres">
										<xs:annotation>
											<xs:documentation>Indicador de presença do comprador no estabelecimento comercial no momento da oepração
											(0-Não se aplica (ex.: Nota Fiscal complementar ou de ajuste;1-Operação presencial;2-Não presencial, internet;3-Não presencial, teleatendimento;4-NFC-e entrega em domicílio;9-Não presencial, outros)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
												<xs:enumeration value="4"/>
												<xs:enumeration value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="procEmi" type="TProcEmi">
										<xs:annotation>
											<xs:documentation>Processo de emissão utilizado com a seguinte codificação:
0 - emissão de NF-e com aplicativo do contribuinte;
1 - emissão de NF-e avulsa pelo Fisco;
2 - emissão de NF-e avulsa, pelo contribuinte com seu certificado digital, através do site
do Fisco;
3- emissão de NF-e pelo contribuinte com aplicativo fornecido pelo Fisco.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="verProc">
										<xs:annotation>
											<xs:documentation>versão do aplicativo utilizado no processo de
emissão</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="20"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:sequence minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informar apenas
para tpEmis diferente de 1</xs:documentation>
										</xs:annotation>
										<xs:element name="dhCont" type="TDateTimeUTC">
											<xs:annotation>
												<xs:documentation>Informar a data e hora de entrada em contingência contingência no formato  (AAAA-MM-DDThh:mm:ssTZD) ex.: 2012-09-01T13:00:00-03:00.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="xJust">
											<xs:annotation>
												<xs:documentation>Informar a Justificativa da entrada</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="15"/>
													<xs:maxLength value="256"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
									<xs:element name="NFref" minOccurs="0" maxOccurs="500">
										<xs:annotation>
											<xs:documentation>Grupo de infromações da NF referenciada</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:choice>
												<xs:element name="refNFe" type="TChNFe">
													<xs:annotation>
														<xs:documentation>Chave de acesso das NF-e referenciadas. Chave de acesso compostas por Código da UF (tabela do IBGE) + AAMM da emissão + CNPJ do Emitente + modelo, série e número da NF-e Referenciada + Código Numérico + DV.</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="refNF">
													<xs:annotation>
														<xs:documentation>Dados da NF modelo 1/1A referenciada</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:element name="cUF" type="TCodUfIBGE">
																<xs:annotation>
																	<xs:documentation>Código da UF do emitente do Documento Fiscal. Utilizar a Tabela do IBGE.</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="AAMM">
																<xs:annotation>
																	<xs:documentation>AAMM da emissão</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:pattern value="[0-9]{2}[0]{1}[1-9]{1}|[0-9]{2}[1]{1}[0-2]{1}"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="CNPJ" type="TCnpj">
																<xs:annotation>
																	<xs:documentation>CNPJ do emitente do documento fiscal referenciado</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="mod">
																<xs:annotation>
																	<xs:documentation>Código do modelo do Documento Fiscal. Utilizar 01 para NF modelo 1/1A</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:enumeration value="01"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="serie" type="TSerie">
																<xs:annotation>
																	<xs:documentation>Série do Documento Fiscal, informar zero se inexistente</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="nNF" type="TNF">
																<xs:annotation>
																	<xs:documentation>Número do Documento Fiscal</xs:documentation>
																</xs:annotation>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
												<xs:element name="refNFP">
													<xs:annotation>
														<xs:documentation>Grupo com as informações NF de produtor referenciada</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:element name="cUF" type="TCodUfIBGE">
																<xs:annotation>
																	<xs:documentation>Código da UF do emitente do Documento FiscalUtilizar a Tabela do IBGE (Anexo IV - Tabela de UF, Município e País)</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="AAMM">
																<xs:annotation>
																	<xs:documentation>AAMM da emissão da NF de produtor</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:pattern value="[0-9]{2}[0]{1}[1-9]{1}|[0-9]{2}[1]{1}[0-2]{1}"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:choice>
																<xs:element name="CNPJ" type="TCnpj">
																	<xs:annotation>
																		<xs:documentation>CNPJ do emitente da NF de produtor</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="CPF" type="TCpf">
																	<xs:annotation>
																		<xs:documentation>CPF do emitente da NF de produtor</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:choice>
															<xs:element name="IE" type="TIeDest">
																<xs:annotation>
																	<xs:documentation>IE do emitente da NF de Produtor</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="mod">
																<xs:annotation>
																	<xs:documentation>Código do modelo do Documento Fiscal - utilizar 04 para NF de produtor  ou 01 para NF Avulsa</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:enumeration value="01"/>
																		<xs:enumeration value="04"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="serie" type="TSerie">
																<xs:annotation>
																	<xs:documentation>Série do Documento Fiscal, informar zero se inexistentesérie</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="nNF" type="TNF">
																<xs:annotation>
																	<xs:documentation>Número do Documento Fiscal - 1 – 999999999</xs:documentation>
																</xs:annotation>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
												<xs:element name="refCTe" type="TChNFe">
													<xs:annotation>
														<xs:documentation>Utilizar esta TAG para referenciar um CT-e emitido anteriormente, vinculada a NF-e atual</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="refECF">
													<xs:annotation>
														<xs:documentation>Grupo do Cupom Fiscal vinculado à NF-e</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:element name="mod">
																<xs:annotation>
																	<xs:documentation>Código do modelo do Documento Fiscal 
Preencher com "2B", quando se tratar de Cupom Fiscal emitido por máquina registradora (não ECF), com "2C", quando se tratar de Cupom Fiscal PDV, ou "2D", quando se tratar de Cupom Fiscal (emitido por ECF)</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:enumeration value="2B"/>
																		<xs:enumeration value="2C"/>
																		<xs:enumeration value="2D"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="nECF">
																<xs:annotation>
																	<xs:documentation>Informar o número de ordem seqüencial do ECF que emitiu o Cupom Fiscal vinculado à NF-e</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:pattern value="[0-9]{1,3}"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="nCOO">
																<xs:annotation>
																	<xs:documentation>Informar o Número do Contador de Ordem de Operação - COO vinculado à NF-e</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:pattern value="[0-9]{1,6}"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
											</xs:choice>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="emit">
							<xs:annotation>
								<xs:documentation>Identificação do emitente</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>Número do CNPJ do emitente</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF do emitente</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão Social ou Nome do emitente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xFant" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome fantasia</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="enderEmit" type="TEnderEmi">
										<xs:annotation>
											<xs:documentation>Endereço do emitente</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="IE" type="TIe">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual do Emitente</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="IEST" type="TIeST" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscricao Estadual do Substituto Tributário</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:sequence minOccurs="0">
										<xs:annotation>
											<xs:documentation>Grupo de informações de interesse da Prefeitura</xs:documentation>
										</xs:annotation>
										<xs:element name="IM">
											<xs:annotation>
												<xs:documentation>Inscrição Municipal</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="1"/>
													<xs:maxLength value="15"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="CNAE" minOccurs="0">
											<xs:annotation>
												<xs:documentation>CNAE Fiscal</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:whiteSpace value="preserve"/>
													<xs:pattern value="[0-9]{7}"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
									<xs:element name="CRT">
										<xs:annotation>
											<xs:documentation>Código de Regime Tributário. 
Este campo será obrigatoriamente preenchido com:
1 – Simples Nacional;
2 – Simples Nacional – excesso de sublimite de receita bruta;
3 – Regime Normal.
</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="avulsa" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Emissão de avulsa, informar os dados do Fisco emitente</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CNPJ" type="TCnpj">
										<xs:annotation>
											<xs:documentation>CNPJ do Órgão emissor</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xOrgao">
										<xs:annotation>
											<xs:documentation>Órgão emitente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="matr">
										<xs:annotation>
											<xs:documentation>Matrícula do agente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xAgente">
										<xs:annotation>
											<xs:documentation>Nome do agente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fone" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Telefone</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{6,14}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="UF" type="TUfEmi">
										<xs:annotation>
											<xs:documentation>Sigla da Unidade da Federação</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="nDAR" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Número do Documento de Arrecadação de Receita</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="dEmi" type="TData" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Data de emissão do DAR (AAAA-MM-DD)</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="vDAR" type="TDec_1302" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Valor Total constante no DAR</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="repEmi">
										<xs:annotation>
											<xs:documentation>Repartição Fiscal emitente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="dPag" type="TData" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Data de pagamento do DAR (AAAA-MM-DD)</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="dest" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Identificação do Destinatário  </xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="idEstrangeiro">
											<xs:annotation>
												<xs:documentation>Identificador do destinatário, em caso de comprador estrangeiro</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:whiteSpace value="preserve"/>
													<xs:pattern value="([!-ÿ]{0}|[!-ÿ]{5,20})?"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:choice>
									<xs:element name="xNome" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Razão Social ou nome do destinatário</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="enderDest" type="TEndereco" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Dados do endereço</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="indIEDest">
										<xs:annotation>
											<xs:documentation>Indicador da IE do destinatário:
1 – Contribuinte ICMSpagamento à vista;
2 – Contribuinte isento de inscrição;
9 – Não Contribuinte</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="IE" type="TIeDestNaoIsento" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual (obrigatório nas operações com contribuintes do ICMS)</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="ISUF" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição na SUFRAMA (Obrigatório nas operações com as áreas com benefícios de incentivos fiscais sob controle da SUFRAMA) PL_005d - 11/08/09 - alterado para aceitar 8 ou 9 dígitos</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{8,9}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="IM" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Municipal do tomador do serviço</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="15"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="email" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informar o e-mail do destinatário. O campo pode ser utilizado para informar o e-mail
de recepção da NF-e indicada pelo destinatário</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:whiteSpace value="preserve"/>
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="retirada" type="TLocal" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Identificação do Local de Retirada (informar apenas quando for diferente do endereço do remetente)</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="entrega" type="TLocal" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Identificação do Local de Entrega (informar apenas quando for diferente do endereço do destinatário)</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="autXML" minOccurs="0" maxOccurs="10">
							<xs:annotation>
								<xs:documentation>Pessoas autorizadas para o download do XML da NF-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:choice>
									<xs:element name="CNPJ" type="TCnpj">
										<xs:annotation>
											<xs:documentation>CNPJ Autorizado</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="CPF" type="TCpf">
										<xs:annotation>
											<xs:documentation>CPF Autorizado</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:choice>
							</xs:complexType>
						</xs:element>
						<xs:element name="det" maxOccurs="990">
							<xs:annotation>
								<xs:documentation>Dados dos detalhes da NF-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="prod">
										<xs:annotation>
											<xs:documentation>Dados dos produtos e serviços da NF-e</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="cProd">
													<xs:annotation>
														<xs:documentation>Código do produto ou serviço. Preencher com CFOP caso se trate de itens não relacionados com mercadorias/produto e que o contribuinte não possua codificação própria
Formato ”CFOP9999”.</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="60"/>
															<xs:minLength value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="cEAN">
													<xs:annotation>
														<xs:documentation>GTIN (Global Trade Item Number) do produto, antigo código EAN ou código de barras</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[0-9]{0}|[0-9]{8}|[0-9]{12,14}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="xProd">
													<xs:annotation>
														<xs:documentation>Descrição do produto ou serviço</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="120"/>
															<xs:minLength value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="NCM">
													<xs:annotation>
														<xs:documentation>Código NCM (8 posições), será permitida a informação do gênero (posição do capítulo do NCM) quando a operação não for de comércio exterior (importação/exportação) ou o produto não seja tributado pelo IPI. Em caso de item de serviço ou item que não tenham produto (Ex. transferência de crédito, crédito do ativo imobilizado, etc.), informar o código 00 (zeros) (v2.0)</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[0-9]{2}|[0-9]{8}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="NVE" minOccurs="0" maxOccurs="8">
													<xs:annotation>
														<xs:documentation>Nomenclatura de Valor aduaneio e Estatístico</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[A-Z]{2}[0-9]{4}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="CEST" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Codigo especificador da Substuicao Tributaria - CEST, que identifica a mercadoria sujeita aos regimes de  substituicao tributária e de antecipação do recolhimento  do imposto
														</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[0-9]{7}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="EXTIPI" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Código EX TIPI (3 posições)</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[0-9]{2,3}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="CFOP">
													<xs:annotation>
														<xs:documentation>Cfop</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[1,2,3,5,6,7]{1}[0-9]{3}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="uCom">
													<xs:annotation>
														<xs:documentation>Unidade comercial</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="6"/>
															<xs:minLength value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="qCom" type="TDec_1104v">
													<xs:annotation>
														<xs:documentation>Quantidade Comercial  do produto, alterado para aceitar de 0 a 4 casas decimais e 11 inteiros.</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vUnCom" type="TDec_1110v">
													<xs:annotation>
														<xs:documentation>Valor unitário de comercialização  - alterado para aceitar 0 a 10 casas decimais e 11 inteiros </xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vProd" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor bruto do produto ou serviço.</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="cEANTrib">
													<xs:annotation>
														<xs:documentation>GTIN (Global Trade Item Number) da unidade tributável, antigo código EAN ou código de barras</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[0-9]{0}|[0-9]{8}|[0-9]{12,14}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="uTrib">
													<xs:annotation>
														<xs:documentation>Unidade Tributável</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="6"/>
															<xs:minLength value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="qTrib" type="TDec_1104v">
													<xs:annotation>
														<xs:documentation>Quantidade Tributável - alterado para aceitar de 0 a 4 casas decimais e 11 inteiros </xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vUnTrib" type="TDec_1110v">
													<xs:annotation>
														<xs:documentation>Valor unitário de tributação - - alterado para aceitar 0 a 10 casas decimais e 11 inteiros </xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vFrete" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Total do Frete</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vSeg" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Total do Seguro</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vDesc" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor do Desconto</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vOutro" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Outras despesas acessórias</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="indTot">
													<xs:annotation>
														<xs:documentation>Este campo deverá ser preenchido com:
 0 – o valor do item (vProd) não compõe o valor total da NF-e (vProd)
 1  – o valor do item (vProd) compõe o valor total da NF-e (vProd)
</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:enumeration value="0"/>
															<xs:enumeration value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="DI" minOccurs="0" maxOccurs="100">
													<xs:annotation>
														<xs:documentation>Delcaração de Importação
(NT 2011/004)</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:element name="nDI">
																<xs:annotation>
																	<xs:documentation>Numero do Documento de Importação DI/DSI/DA/DRI-E (DI/DSI/DA/DRI-E) (NT2011/004)</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="TString">
																		<xs:minLength value="1"/>
																		<xs:maxLength value="12"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="dDI" type="TData">
																<xs:annotation>
																	<xs:documentation>Data de registro da DI/DSI/DA (AAAA-MM-DD)</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="xLocDesemb">
																<xs:annotation>
																	<xs:documentation>Local do desembaraço aduaneiro</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="TString">
																		<xs:minLength value="1"/>
																		<xs:maxLength value="60"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="UFDesemb" type="TUfEmi">
																<xs:annotation>
																	<xs:documentation>UF onde ocorreu o desembaraço aduaneiro</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="dDesemb" type="TData">
																<xs:annotation>
																	<xs:documentation>Data do desembaraço aduaneiro (AAAA-MM-DD)</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="tpViaTransp">
																<xs:annotation>
																	<xs:documentation>Via de transporte internacional informada na DI
																	1-Maritima;2-Fluvial;3-Lacustre;4-Aerea;5-Postal;6-Ferroviaria;7-Rodoviaria;8-Conduto;9-Meios Proprios;10-Entrada/Saida Ficta.</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:enumeration value="1"/>
																		<xs:enumeration value="2"/>
																		<xs:enumeration value="3"/>
																		<xs:enumeration value="4"/>
																		<xs:enumeration value="5"/>
																		<xs:enumeration value="6"/>
																		<xs:enumeration value="7"/>
																		<xs:enumeration value="8"/>
																		<xs:enumeration value="9"/>
																		<xs:enumeration value="10"/>
																		<xs:enumeration value="11"/>
																		<xs:enumeration value="12"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="vAFRMM" type="TDec_1302" minOccurs="0">
																<xs:annotation>
																	<xs:documentation>Valor Adicional ao frete para renovação de marinha mercante</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="tpIntermedio">
																<xs:annotation>
																	<xs:documentation>Forma de Importação quanto a intermediação 
																	1-por conta propria;2-por conta e ordem;3-encomenda</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:enumeration value="1"/>
																		<xs:enumeration value="2"/>
																		<xs:enumeration value="3"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="CNPJ" type="TCnpj" minOccurs="0">
																<xs:annotation>
																	<xs:documentation>CNPJ do adquirente ou do encomendante</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="UFTerceiro" type="TUfEmi" minOccurs="0">
																<xs:annotation>
																	<xs:documentation>Sigla da UF do adquirente ou do encomendante</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="cExportador">
																<xs:annotation>
																	<xs:documentation>Código do exportador (usado nos sistemas internos de informação do emitente da NF-e)</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="TString">
																		<xs:minLength value="1"/>
																		<xs:maxLength value="60"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="adi" maxOccurs="100">
																<xs:annotation>
																	<xs:documentation>Adições (NT 2011/004)</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="nAdicao">
																			<xs:annotation>
																				<xs:documentation>Número da Adição</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:pattern value="[1-9]{1}[0-9]{0,2}"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:element name="nSeqAdic">
																			<xs:annotation>
																				<xs:documentation>Número seqüencial do item dentro da Adição</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:pattern value="[1-9]{1}[0-9]{0,2}"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:element name="cFabricante">
																			<xs:annotation>
																				<xs:documentation>Código do fabricante estrangeiro (usado nos sistemas internos de informação do emitente da NF-e)</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="TString">
																					<xs:minLength value="1"/>
																					<xs:maxLength value="60"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:element name="vDescDI" type="TDec_1302Opc" minOccurs="0">
																			<xs:annotation>
																				<xs:documentation>Valor do desconto do item da DI – adição</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="nDraw" minOccurs="0">
																			<xs:annotation>
																				<xs:documentation>Número do ato concessório de Drawback</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:pattern value="[0-9]{0,11}"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
												<xs:element name="detExport" minOccurs="0" maxOccurs="500">
													<xs:annotation>
														<xs:documentation>Detalhe da exportação</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:element name="nDraw" minOccurs="0">
																<xs:annotation>
																	<xs:documentation>Número do ato concessório de Drawback</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:pattern value="[0-9]{0,11}"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="exportInd" minOccurs="0">
																<xs:annotation>
																	<xs:documentation>Exportação indireta</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="nRE">
																			<xs:annotation>
																				<xs:documentation>Registro de exportação</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:pattern value="[0-9]{0,12}"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:element name="chNFe" type="TChNFe">
																			<xs:annotation>
																				<xs:documentation>Chave de acesso da NF-e recebida para exportação</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="qExport" type="TDec_1104v">
																			<xs:annotation>
																				<xs:documentation>Quantidade do item efetivamente exportado</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
												<xs:element name="xPed" minOccurs="0">
													<xs:annotation>
														<xs:documentation>pedido de compra - Informação de interesse do emissor para controle do B2B.</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="15"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="nItemPed" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Número do Item do Pedido de Compra - Identificação do número do item do pedido de Compra</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[0-9]{1,6}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="nFCI" type="TGuid" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Número de controle da FCI - Ficha de Conteúdo de Importação.</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:choice minOccurs="0">
													<xs:annotation>
														<xs:documentation>Informações específicas de produtos e serviços</xs:documentation>
													</xs:annotation>
													<xs:element name="veicProd">
														<xs:annotation>
															<xs:documentation>Veículos novos</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpOp">
																	<xs:annotation>
																		<xs:documentation>Tipo da Operação (1 - Venda concessionária; 2 - Faturamento direto; 3 - Venda direta; 0 - Outros)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="0"/>
																			<xs:enumeration value="1"/>
																			<xs:enumeration value="2"/>
																			<xs:enumeration value="3"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="chassi">
																	<xs:annotation>
																		<xs:documentation>Chassi do veículo - VIN (código-identificação-veículo)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:length value="17"/>
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[A-Z0-9]+"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="cCor">
																	<xs:annotation>
																		<xs:documentation>Cor do veículo (código de cada montadora)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="4"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="xCor">
																	<xs:annotation>
																		<xs:documentation>Descrição da cor</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="40"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="pot">
																	<xs:annotation>
																		<xs:documentation>Potência máxima do motor do veículo em cavalo vapor (CV). (potência-veículo)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="4"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="cilin">
																	<xs:annotation>
																		<xs:documentation>Capacidade voluntária do motor expressa em centímetros cúbicos (CC). (cilindradas)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="4"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="pesoL">
																	<xs:annotation>
																		<xs:documentation>Peso líquido</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="9"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="pesoB">
																	<xs:annotation>
																		<xs:documentation>Peso bruto</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="9"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="nSerie">
																	<xs:annotation>
																		<xs:documentation>Serial (série)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="9"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="tpComb">
																	<xs:annotation>
																		<xs:documentation>Tipo de combustível-Tabela RENAVAM: 01-Álcool; 02-Gasolina; 03-Diesel; 16-Álcool/Gas.; 17-Gas./Álcool/GNV; 18-Gasolina/Elétrico</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="2"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="nMotor">
																	<xs:annotation>
																		<xs:documentation>Número do motor</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="21"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="CMT">
																	<xs:annotation>
																		<xs:documentation>CMT-Capacidade Máxima de Tração - em Toneladas 4 casas decimais</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="9"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="dist">
																	<xs:annotation>
																		<xs:documentation>Distância entre eixos</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="4"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="anoMod">
																	<xs:annotation>
																		<xs:documentation>Ano Modelo de Fabricação</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[0-9]{4}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="anoFab">
																	<xs:annotation>
																		<xs:documentation>Ano de Fabricação</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[0-9]{4}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="tpPint">
																	<xs:annotation>
																		<xs:documentation>Tipo de pintura</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:length value="1"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="tpVeic">
																	<xs:annotation>
																		<xs:documentation>Tipo de veículo (utilizar tabela RENAVAM)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[0-9]{1,2}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="espVeic">
																	<xs:annotation>
																		<xs:documentation>Espécie de veículo (utilizar tabela RENAVAM)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[0-9]{1}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="VIN">
																	<xs:annotation>
																		<xs:documentation>Informa-se o veículo tem VIN (chassi) remarcado.
R-Remarcado
N-NormalVIN </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:length value="1"/>
																			<xs:enumeration value="R"/>
																			<xs:enumeration value="N"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="condVeic">
																	<xs:annotation>
																		<xs:documentation>Condição do veículo (1 - acabado; 2 - inacabado; 3 - semi-acabado)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="1"/>
																			<xs:enumeration value="2"/>
																			<xs:enumeration value="3"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="cMod">
																	<xs:annotation>
																		<xs:documentation>Código Marca Modelo (utilizar tabela RENAVAM)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[0-9]{1,6}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="cCorDENATRAN">
																	<xs:annotation>
																		<xs:documentation>Código da Cor Segundo as regras de pré-cadastro do DENATRAN: 01-AMARELO;02-AZUL;03-BEGE;04-BRANCA;05-CINZA;06-DOURADA;07-GRENA 
08-LARANJA;09-MARROM;10-PRATA;11-PRETA;12-ROSA;13-ROXA;14-VERDE;15-VERMELHA;16-FANTASIA</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:minLength value="1"/>
																			<xs:maxLength value="2"/>
																			<xs:pattern value="[0-9]{1,2}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="lota">
																	<xs:annotation>
																		<xs:documentation>Quantidade máxima de permitida de passageiros sentados, inclusive motorista.</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="3"/>
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[0-9]{1,3}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="tpRest">
																	<xs:annotation>
																		<xs:documentation>Restrição
0 - Não há;
1 - Alienação Fiduciária;
2 - Arrendamento Mercantil;
3 - Reserva de Domínio;
4 - Penhor de Veículos;
9 - outras.</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="0"/>
																			<xs:enumeration value="1"/>
																			<xs:enumeration value="2"/>
																			<xs:enumeration value="3"/>
																			<xs:enumeration value="4"/>
																			<xs:enumeration value="9"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="med" maxOccurs="500">
														<xs:annotation>
															<xs:documentation>grupo do detalhamento de Medicamentos e de matérias-primas farmacêuticas</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="nLote">
																	<xs:annotation>
																		<xs:documentation>Número do lote do medicamento</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="qLote" type="TDec_0803v">
																	<xs:annotation>
																		<xs:documentation>Quantidade de produtos no lote</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="dFab" type="TData">
																	<xs:annotation>
																		<xs:documentation>Data de Fabricação do medicamento (AAAA-MM-DD)</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="dVal" type="TData">
																	<xs:annotation>
																		<xs:documentation>Data de validade do medicamento (AAAA-MM-DD)</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="vPMC" type="TDec_1302">
																	<xs:annotation>
																		<xs:documentation>Preço Máximo ao Consumidor</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="arma" maxOccurs="500">
														<xs:annotation>
															<xs:documentation>Armamentos</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpArma">
																	<xs:annotation>
																		<xs:documentation>Indicador do tipo de arma de fogo (0 - Uso permitido; 1 - Uso restrito)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="0"/>
																			<xs:enumeration value="1"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="nSerie">
																	<xs:annotation>
																		<xs:documentation>Número de série da arma</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="15"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="nCano">
																	<xs:annotation>
																		<xs:documentation>Número de série do cano</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="15"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="descr">
																	<xs:annotation>
																		<xs:documentation>Descrição completa da arma, compreendendo: calibre, marca, capacidade, tipo de funcionamento, comprimento e demais elementos que permitam a sua perfeita identificação.</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="256"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="comb">
														<xs:annotation>
															<xs:documentation>Informar apenas para operações com combustíveis líquidos</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="cProdANP">
																	<xs:annotation>
																		<xs:documentation>Código de produto da ANP. codificação de produtos do SIMP (http://www.anp.gov.br)</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[0-9]{9}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="pMixGN" type="TDec_0204v" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Percentual de gas natural para o produto GLP</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="CODIF" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Código de autorização / registro do CODIF. Informar apenas quando a UF utilizar o CODIF (Sistema de Controle do 			Diferimento do Imposto nas Operações com AEAC - Álcool Etílico Anidro Combustível).</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:pattern value="[0-9]{1,21}"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="qTemp" type="TDec_1204temperatura" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Quantidade de combustível
faturada à temperatura ambiente.
Informar quando a quantidade
faturada informada no campo
qCom (I10) tiver sido ajustada para
uma temperatura diferente da
ambiente.</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="UFCons" type="TUf">
																	<xs:annotation>
																		<xs:documentation>Sigla da UF de Consumo</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="CIDE" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>CIDE Combustíveis</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="qBCProd" type="TDec_1204v">
																				<xs:annotation>
																					<xs:documentation>BC do CIDE ( Quantidade comercializada) </xs:documentation>
																				</xs:annotation>
																			</xs:element>
																			<xs:element name="vAliqProd" type="TDec_1104">
																				<xs:annotation>
																					<xs:documentation>Alíquota do CIDE  (em reais)</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																			<xs:element name="vCIDE" type="TDec_1302">
																				<xs:annotation>
																					<xs:documentation>Valor do CIDE</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="encerrante" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Informações do grupo de "encerrante"</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="nBico">
																				<xs:annotation>
																					<xs:documentation>Numero de identificação do Bico utilizado no abastecimento</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:whiteSpace value="preserve"/>
																						<xs:pattern value="[0-9]{1,3}"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="nBomba" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Numero de identificação da bomba ao qual o bico está interligado</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:whiteSpace value="preserve"/>
																						<xs:pattern value="[0-9]{1,3}"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="nTanque">
																				<xs:annotation>
																					<xs:documentation>Numero de identificação do tanque ao qual o bico está interligado</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:whiteSpace value="preserve"/>
																						<xs:pattern value="[0-9]{1,3}"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="vEncIni" type="TDec_1203">
																				<xs:annotation>
																					<xs:documentation>Valor do Encerrante no ínicio do abastecimento</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																			<xs:element name="vEncFin" type="TDec_1203">
																				<xs:annotation>
																					<xs:documentation>Valor do Encerrante no final do abastecimento</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="nRECOPI">
														<xs:annotation>
															<xs:documentation>Número do RECOPI</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:maxLength value="20"/>
																<xs:pattern value="[0-9]{20}"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:choice>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="imposto">
										<xs:annotation>
											<xs:documentation>Tributos incidentes nos produtos ou serviços da NF-e</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="vTotTrib" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor estimado total de impostos federais, estaduais e municipais</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:choice>
													<xs:sequence>
														<xs:element name="ICMS">
															<xs:annotation>
																<xs:documentation>Dados do ICMS Normal e ST</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:choice>
																	<xs:element name="ICMS00">
																		<xs:annotation>
																			<xs:documentation>Tributação pelo ICMS
00 - Tributada integralmente</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributção pelo ICMS
00 - Tributada integralmente
</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="00"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBC">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS:
0 - Margem Valor Agregado (%);
1 - Pauta (valor);
2 - Preço Tabelado Máximo (valor);
3 - Valor da Operação.</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="vBC" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMS" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMS" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMS10">
																		<xs:annotation>
																			<xs:documentation>Tributação pelo ICMS
10 - Tributada e com cobrança do ICMS por substituição tributária </xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>10 - Tributada e com cobrança do ICMS por substituição tributária </xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="10"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBC">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS:
0 - Margem Valor Agregado (%);
1 - Pauta (valor);
2 - Preço Tabelado Máximo (valor);
3 - Valor da Operação.</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="vBC" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMS" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMS" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="modBCST">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS ST:
0 – Preço tabelado ou máximo  sugerido;
1 - Lista Negativa (valor);
2 - Lista Positiva (valor);
3 - Lista Neutra (valor);
4 - Margem Valor Agregado (%);
5 - Pauta (valor);</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																							<xs:enumeration value="4"/>
																							<xs:enumeration value="5"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pMVAST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual da Margem de Valor Adicionado ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pRedBCST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC ICMS ST </xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBCST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMSST" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMS20">
																		<xs:annotation>
																			<xs:documentation>Tributção pelo ICMS
20 - Com redução de base de cálculo </xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributção pelo ICMS
20 - Com redução de base de cálculo</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="20"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBC">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS:
0 - Margem Valor Agregado (%);
1 - Pauta (valor);
2 - Preço Tabelado Máximo (valor);
3 - Valor da Operação.</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pRedBC" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBC" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMS" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMS" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:sequence minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Grupo desoneração</xs:documentation>
																					</xs:annotation>
																					<xs:element name="vICMSDeson" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS de desoneração</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="motDesICMS">
																						<xs:annotation>
																							<xs:documentation>Motivo da desoneração do ICMS:3-Uso na agropecuária;9-Outros;12-Fomento agropecuário</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="3"/>
																								<xs:enumeration value="9"/>
																								<xs:enumeration value="12"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																				</xs:sequence>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMS30">
																		<xs:annotation>
																			<xs:documentation>Tributação pelo ICMS
30 - Isenta ou não tributada e com cobrança do ICMS por substituição tributária</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributção pelo ICMS
30 - Isenta ou não tributada e com cobrança do ICMS por substituição tributária </xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="30"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBCST">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS ST:
0 – Preço tabelado ou máximo  sugerido;
1 - Lista Negativa (valor);
2 - Lista Positiva (valor);
3 - Lista Neutra (valor);
4 - Margem Valor Agregado (%);
5 - Pauta (valor).</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																							<xs:enumeration value="4"/>
																							<xs:enumeration value="5"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pMVAST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual da Margem de Valor Adicionado ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pRedBCST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC ICMS ST </xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBCST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMSST" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:sequence minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Grupo desoneração</xs:documentation>
																					</xs:annotation>
																					<xs:element name="vICMSDeson" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS de desoneração</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="motDesICMS">
																						<xs:annotation>
																							<xs:documentation>Motivo da desoneração do ICMS:6-Utilitários Motocicleta AÁrea Livre;7-SUFRAMA;9-Outros</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="6"/>
																								<xs:enumeration value="7"/>
																								<xs:enumeration value="9"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																				</xs:sequence>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMS40">
																		<xs:annotation>
																			<xs:documentation>Tributação pelo ICMS
40 - Isenta 
41 - Não tributada 
50 - Suspensão  </xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributação pelo ICMS 
40 - Isenta 
41 - Não tributada 
50 - Suspensão 
51 - Diferimento </xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="40"/>
																							<xs:enumeration value="41"/>
																							<xs:enumeration value="50"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:sequence minOccurs="0">
																					<xs:element name="vICMSDeson" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>O valor do ICMS será informado apenas nas operações com veículos beneficiados com a desoneração condicional do ICMS.</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="motDesICMS">
																						<xs:annotation>
																							<xs:documentation>Este campo será preenchido quando o campo anterior estiver preenchido.
Informar o motivo da desoneração:
1 – Táxi;
3 – Produtor Agropecuário;
4 – Frotista/Locadora;
5 – Diplomático/Consular;
6 – Utilitários e Motocicletas da Amazônia Ocidental e Áreas de Livre Comércio (Resolução 714/88 e 790/94 – CONTRAN e suas alterações);
7 – SUFRAMA;
8 - Venda a órgão Público;
9 – Outros
10- Deficiente Condutor
11- Deficiente não condutor
16 - Olimpíadas Rio 2016
							</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="1"/>
																								<xs:enumeration value="3"/>
																								<xs:enumeration value="4"/>
																								<xs:enumeration value="5"/>
																								<xs:enumeration value="6"/>
																								<xs:enumeration value="7"/>
																								<xs:enumeration value="8"/>
																								<xs:enumeration value="9"/>
																								<xs:enumeration value="10"/>
																								<xs:enumeration value="11"/>
																								<xs:enumeration value="16"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																				</xs:sequence>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMS51">
																		<xs:annotation>
																			<xs:documentation>Tributção pelo ICMS
51 - Diferimento
A exigência do preenchimento das informações do ICMS diferido fica à critério de cada UF.</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributção pelo ICMS
20 - Com redução de base de cálculo</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="51"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBC" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS:
0 - Margem Valor Agregado (%);
1 - Pauta (valor);
2 - Preço Tabelado Máximo (valor);
3 - Valor da Operação.</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pRedBC" type="TDec_0302a04" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBC" type="TDec_1302" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMS" type="TDec_0302a04" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSOp" type="TDec_1302" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS da Operação</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pDif" type="TDec_0302a04Max100" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual do diferemento</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSDif" type="TDec_1302" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS da diferido</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMS" type="TDec_1302" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMS60">
																		<xs:annotation>
																			<xs:documentation>Tributação pelo ICMS
60 - ICMS cobrado anteriormente por substituição tributária </xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributação pelo ICMS 
60 - ICMS cobrado anteriormente por substituição tributária </xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="60"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:sequence minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>NT2010/004</xs:documentation>
																					</xs:annotation>
																					<xs:element name="vBCSTRet" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor da BC do ICMS ST retido anteriormente</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vICMSSTRet" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS ST retido anteriormente</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																				</xs:sequence>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMS70">
																		<xs:annotation>
																			<xs:documentation>Tributação pelo ICMS 
70 - Com redução de base de cálculo e cobrança do ICMS por substituição tributária </xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributção pelo ICMS
70 - Com redução de base de cálculo e cobrança do ICMS por substituição tributária </xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="70"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBC">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS:
0 - Margem Valor Agregado (%);
1 - Pauta (valor);
2 - Preço Tabelado Máximo (valor);
3 - Valor da Operação.</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pRedBC" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBC" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMS" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMS" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="modBCST">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS ST:
0 – Preço tabelado ou máximo  sugerido;
1 - Lista Negativa (valor);
2 - Lista Positiva (valor);
3 - Lista Neutra (valor);
4 - Margem Valor Agregado (%);
5 - Pauta (valor).</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																							<xs:enumeration value="4"/>
																							<xs:enumeration value="5"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pMVAST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual da Margem de Valor Adicionado ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pRedBCST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC ICMS ST </xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBCST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMSST" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:sequence minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Grupo desoneração</xs:documentation>
																					</xs:annotation>
																					<xs:element name="vICMSDeson" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS de desoneração</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="motDesICMS">
																						<xs:annotation>
																							<xs:documentation>Motivo da desoneração do ICMS:3-Uso na agropecuária;9-Outros;12-Fomento agropecuário</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="3"/>
																								<xs:enumeration value="9"/>
																								<xs:enumeration value="12"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																				</xs:sequence>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMS90">
																		<xs:annotation>
																			<xs:documentation>Tributação pelo ICMS
90 - Outras</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributção pelo ICMS
90 - Outras</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="90"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:sequence minOccurs="0">
																					<xs:element name="modBC">
																						<xs:annotation>
																							<xs:documentation>Modalidade de determinação da BC do ICMS: 
0 - Margem Valor Agregado (%);
1 - Pauta (valor);
2 - Preço Tabelado Máximo (valor);
3 - Valor da Operação.</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="0"/>
																								<xs:enumeration value="1"/>
																								<xs:enumeration value="2"/>
																								<xs:enumeration value="3"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																					<xs:element name="vBC" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor da BC do ICMS</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="pRedBC" type="TDec_0302a04Opc" minOccurs="0">
																						<xs:annotation>
																							<xs:documentation>Percentual de redução da BC</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="pICMS" type="TDec_0302a04">
																						<xs:annotation>
																							<xs:documentation>Alíquota do ICMS</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vICMS" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																				</xs:sequence>
																				<xs:sequence minOccurs="0">
																					<xs:element name="modBCST">
																						<xs:annotation>
																							<xs:documentation>Modalidade de determinação da BC do ICMS ST:
0 – Preço tabelado ou máximo  sugerido;
1 - Lista Negativa (valor);
2 - Lista Positiva (valor);
3 - Lista Neutra (valor);
4 - Margem Valor Agregado (%);
5 - Pauta (valor).</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="0"/>
																								<xs:enumeration value="1"/>
																								<xs:enumeration value="2"/>
																								<xs:enumeration value="3"/>
																								<xs:enumeration value="4"/>
																								<xs:enumeration value="5"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																					<xs:element name="pMVAST" type="TDec_0302a04Opc" minOccurs="0">
																						<xs:annotation>
																							<xs:documentation>Percentual da Margem de Valor Adicionado ICMS ST</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="pRedBCST" type="TDec_0302a04Opc" minOccurs="0">
																						<xs:annotation>
																							<xs:documentation>Percentual de redução da BC ICMS ST </xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vBCST" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor da BC do ICMS ST</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="pICMSST" type="TDec_0302a04">
																						<xs:annotation>
																							<xs:documentation>Alíquota do ICMS ST</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vICMSST" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS ST</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																				</xs:sequence>
																				<xs:sequence minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Grupo desoneração</xs:documentation>
																					</xs:annotation>
																					<xs:element name="vICMSDeson" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS de desoneração</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="motDesICMS">
																						<xs:annotation>
																							<xs:documentation>Motivo da desoneração do ICMS:3-Uso na agropecuária;9-Outros;12-Fomento agropecuário</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="3"/>
																								<xs:enumeration value="9"/>
																								<xs:enumeration value="12"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																				</xs:sequence>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMSPart">
																		<xs:annotation>
																			<xs:documentation>Partilha do ICMS entre a UF de origem e UF de destino ou a UF definida na legislação
Operação interestadual para consumidor final com partilha do ICMS  devido na operação entre a UF de origem e a UF do destinatário ou ou a UF definida na legislação. (Ex. UF da concessionária de entrega do  veículos)</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributação pelo ICMS 
10 - Tributada e com cobrança do ICMS por substituição tributária;
90 – Outros.</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="10"/>
																							<xs:enumeration value="90"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBC">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS: 
0 - Margem Valor Agregado (%);
1 - Pauta (valor);
2 - Preço Tabelado Máximo (valor);
3 - Valor da Operação.</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="vBC" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pRedBC" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMS" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMS" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="modBCST">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS ST:
0 – Preço tabelado ou máximo  sugerido;
1 - Lista Negativa (valor);
2 - Lista Positiva (valor);
3 - Lista Neutra (valor);
4 - Margem Valor Agregado (%);
5 - Pauta (valor).</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																							<xs:enumeration value="4"/>
																							<xs:enumeration value="5"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pMVAST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual da Margem de Valor Adicionado ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pRedBCST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBCST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMSST" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS ST</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pBCOp" type="TDec_0302a04Opc">
																					<xs:annotation>
																						<xs:documentation>Percentual para determinação do valor  da Base de Cálculo da operação própria.</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="UFST" type="TUf">
																					<xs:annotation>
																						<xs:documentation>Sigla da UF para qual é devido o ICMS ST da operação.</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMSST">
																		<xs:annotation>
																			<xs:documentation>Grupo de informação do ICMSST devido para a UF de destino, nas operações interestaduais de produtos que tiveram retenção antecipada de ICMS por ST na UF do remetente. Repasse via Substituto Tributário.</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CST">
																					<xs:annotation>
																						<xs:documentation>Tributção pelo ICMS
41-Não Tributado</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="41"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="vBCSTRet" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Informar o valor da BC do ICMS ST retido na UF remetente</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSSTRet" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation> Informar o valor do ICMS ST retido na UF remetente (iv2.0))</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBCSTDest" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation> Informar o valor da BC do ICMS ST da UF destino</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSSTDest" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Informar o valor da BC do ICMS ST da UF destino (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMSSN101">
																		<xs:annotation>
																			<xs:documentation>Tributação do ICMS pelo SIMPLES NACIONAL e CSOSN=101 (v.2.0)</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
(v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CSOSN">
																					<xs:annotation>
																						<xs:documentation>101- Tributada pelo Simples Nacional com permissão de crédito. (v.2.0)</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="101"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pCredSN" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota aplicável de cálculo do crédito (Simples Nacional). (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vCredICMSSN" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor crédito do ICMS que pode ser aproveitado nos termos do art. 23 da LC 123 (Simples Nacional) (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMSSN102">
																		<xs:annotation>
																			<xs:documentation>Tributação do ICMS pelo SIMPLES NACIONAL e CSOSN=102, 103, 300 ou 400 (v.2.0))</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
(v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CSOSN">
																					<xs:annotation>
																						<xs:documentation>102- Tributada pelo Simples Nacional sem permissão de crédito. 
103 – Isenção do ICMS  no Simples Nacional para faixa de receita bruta.
300 – Imune.
400 – Não tributda pelo Simples Nacional (v.2.0) (v.2.0)</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="102"/>
																							<xs:enumeration value="103"/>
																							<xs:enumeration value="300"/>
																							<xs:enumeration value="400"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMSSN201">
																		<xs:annotation>
																			<xs:documentation>Tributação do ICMS pelo SIMPLES NACIONAL e CSOSN=201 (v.2.0)</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>Origem da mercadoria:
0 – Nacional;
1 – Estrangeira – Importação direta;
2 – Estrangeira – Adquirida no mercado interno. (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CSOSN">
																					<xs:annotation>
																						<xs:documentation>201- Tributada pelo Simples Nacional com permissão de crédito e com cobrança do ICMS por Substituição Tributária (v.2.0)</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="201"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBCST">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS ST:
0 – Preço tabelado ou máximo  sugerido;
1 - Lista Negativa (valor);
2 - Lista Positiva (valor);
3 - Lista Neutra (valor);
4 - Margem Valor Agregado (%);
5 - Pauta (valor). (v2.0)</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																							<xs:enumeration value="4"/>
																							<xs:enumeration value="5"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pMVAST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual da Margem de Valor Adicionado ICMS ST (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pRedBCST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC ICMS ST  (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBCST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS ST (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMSST" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS ST (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS ST (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pCredSN" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota aplicável de cálculo do crédito (Simples Nacional). (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vCredICMSSN" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor crédito do ICMS que pode ser aproveitado nos termos do art. 23 da LC 123 (Simples Nacional) (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMSSN202">
																		<xs:annotation>
																			<xs:documentation>Tributação do ICMS pelo SIMPLES NACIONAL e CSOSN=202 ou 203 (v.2.0)</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>Origem da mercadoria:
0 – Nacional;
1 – Estrangeira – Importação direta;
2 – Estrangeira – Adquirida no mercado interno. (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CSOSN">
																					<xs:annotation>
																						<xs:documentation>202- Tributada pelo Simples Nacional sem permissão de crédito e com cobrança do ICMS por Substituição Tributária;
203-  Isenção do ICMS nos Simples Nacional para faixa de receita bruta e com cobrança do ICMS por Substituição Tributária (v.2.0)</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="202"/>
																							<xs:enumeration value="203"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="modBCST">
																					<xs:annotation>
																						<xs:documentation>Modalidade de determinação da BC do ICMS ST:
0 – Preço tabelado ou máximo  sugerido;
1 - Lista Negativa (valor);
2 - Lista Positiva (valor);
3 - Lista Neutra (valor);
4 - Margem Valor Agregado (%);
5 - Pauta (valor). (v2.0)</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="0"/>
																							<xs:enumeration value="1"/>
																							<xs:enumeration value="2"/>
																							<xs:enumeration value="3"/>
																							<xs:enumeration value="4"/>
																							<xs:enumeration value="5"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:element name="pMVAST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual da Margem de Valor Adicionado ICMS ST (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pRedBCST" type="TDec_0302a04Opc" minOccurs="0">
																					<xs:annotation>
																						<xs:documentation>Percentual de redução da BC ICMS ST  (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vBCST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do ICMS ST (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pICMSST" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do ICMS ST (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vICMSST" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor do ICMS ST (v2.0)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMSSN500">
																		<xs:annotation>
																			<xs:documentation>Tributação do ICMS pelo SIMPLES NACIONAL,CRT=1 – Simples Nacional e CSOSN=500 (v.2.0)</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CSOSN">
																					<xs:annotation>
																						<xs:documentation>500 – ICMS cobrado anterirmente por substituição tributária (substituído) ou por antecipação
(v.2.0)</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="500"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:sequence minOccurs="0">
																					<xs:annotation>
																						<xs:documentation/>
																					</xs:annotation>
																					<xs:element name="vBCSTRet" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor da BC do ICMS ST retido anteriormente (v2.0) </xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vICMSSTRet" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS ST retido anteriormente  (v2.0)</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																				</xs:sequence>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																	<xs:element name="ICMSSN900">
																		<xs:annotation>
																			<xs:documentation>Tributação do ICMS pelo SIMPLES NACIONAL, CRT=1 – Simples Nacional e CSOSN=900 (v2.0)</xs:documentation>
																		</xs:annotation>
																		<xs:complexType>
																			<xs:sequence>
																				<xs:element name="orig" type="Torig">
																					<xs:annotation>
																						<xs:documentation>origem da mercadoria: 0 - Nacional 
1 - Estrangeira - Importação direta 
2 - Estrangeira - Adquirida no mercado interno 
</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="CSOSN">
																					<xs:annotation>
																						<xs:documentation>Tributação pelo ICMS 900 - Outros(v2.0)</xs:documentation>
																					</xs:annotation>
																					<xs:simpleType>
																						<xs:restriction base="xs:string">
																							<xs:whiteSpace value="preserve"/>
																							<xs:enumeration value="900"/>
																						</xs:restriction>
																					</xs:simpleType>
																				</xs:element>
																				<xs:sequence minOccurs="0">
																					<xs:element name="modBC">
																						<xs:annotation>
																							<xs:documentation>Modalidade de determinação da BC do ICMS: 
0 - Margem Valor Agregado (%);
1 - Pauta (valor);
2 - Preço Tabelado Máximo (valor);
3 - Valor da Operação.</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="0"/>
																								<xs:enumeration value="1"/>
																								<xs:enumeration value="2"/>
																								<xs:enumeration value="3"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																					<xs:element name="vBC" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor da BC do ICMS</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="pRedBC" type="TDec_0302a04Opc" minOccurs="0">
																						<xs:annotation>
																							<xs:documentation>Percentual de redução da BC</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="pICMS" type="TDec_0302a04">
																						<xs:annotation>
																							<xs:documentation>Alíquota do ICMS</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vICMS" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																				</xs:sequence>
																				<xs:sequence minOccurs="0">
																					<xs:element name="modBCST">
																						<xs:annotation>
																							<xs:documentation>Modalidade de determinação da BC do ICMS ST:
0 – Preço tabelado ou máximo  sugerido;
1 - Lista Negativa (valor);
2 - Lista Positiva (valor);
3 - Lista Neutra (valor);
4 - Margem Valor Agregado (%);
5 - Pauta (valor).</xs:documentation>
																						</xs:annotation>
																						<xs:simpleType>
																							<xs:restriction base="xs:string">
																								<xs:whiteSpace value="preserve"/>
																								<xs:enumeration value="0"/>
																								<xs:enumeration value="1"/>
																								<xs:enumeration value="2"/>
																								<xs:enumeration value="3"/>
																								<xs:enumeration value="4"/>
																								<xs:enumeration value="5"/>
																							</xs:restriction>
																						</xs:simpleType>
																					</xs:element>
																					<xs:element name="pMVAST" type="TDec_0302a04Opc" minOccurs="0">
																						<xs:annotation>
																							<xs:documentation>Percentual da Margem de Valor Adicionado ICMS ST</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="pRedBCST" type="TDec_0302a04Opc" minOccurs="0">
																						<xs:annotation>
																							<xs:documentation>Percentual de redução da BC ICMS ST </xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vBCST" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor da BC do ICMS ST</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="pICMSST" type="TDec_0302a04">
																						<xs:annotation>
																							<xs:documentation>Alíquota do ICMS ST</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vICMSST" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor do ICMS ST</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																				</xs:sequence>
																				<xs:sequence minOccurs="0">
																					<xs:element name="pCredSN" type="TDec_0302a04">
																						<xs:annotation>
																							<xs:documentation>Alíquota aplicável de cálculo do crédito (Simples Nacional). (v2.0)</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																					<xs:element name="vCredICMSSN" type="TDec_1302">
																						<xs:annotation>
																							<xs:documentation>Valor crédito do ICMS que pode ser aproveitado nos termos do art. 23 da LC 123 (Simples Nacional) (v2.0)</xs:documentation>
																						</xs:annotation>
																					</xs:element>
																				</xs:sequence>
																			</xs:sequence>
																		</xs:complexType>
																	</xs:element>
																</xs:choice>
															</xs:complexType>
														</xs:element>
														<xs:element name="IPI" type="TIpi" minOccurs="0"/>
														<xs:element name="II" minOccurs="0">
															<xs:annotation>
																<xs:documentation>Dados do Imposto de Importação</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:sequence>
																	<xs:element name="vBC" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Base da BC do Imposto de Importação</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vDespAdu" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor das despesas aduaneiras</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vII" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor do Imposto de Importação</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vIOF" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor do Imposto sobre Operações Financeiras</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:sequence>
															</xs:complexType>
														</xs:element>
													</xs:sequence>
													<xs:sequence>
														<xs:element name="IPI" type="TIpi" minOccurs="0"/>
														<xs:element name="ISSQN">
															<xs:annotation>
																<xs:documentation>ISSQN</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:sequence>
																	<xs:element name="vBC" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor da BC do ISSQN</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vAliq" type="TDec_0302a04">
																		<xs:annotation>
																			<xs:documentation>Alíquota do ISSQN</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vISSQN" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor da do ISSQN</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="cMunFG" type="TCodMunIBGE">
																		<xs:annotation>
																			<xs:documentation>Informar o município de ocorrência do fato gerador do ISSQN. Utilizar a Tabela do IBGE (Anexo VII - Tabela de UF, Município e País). “Atenção, não vincular com os campos B12, C10 ou E10” v2.0</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="cListServ" type="TCListServ">
																		<xs:annotation>
																			<xs:documentation>Informar o Item da lista de serviços da LC 116/03 em que se classifica o serviço.</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vDeducao" type="TDec_1302Opc" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Valor dedução para redução da base de cálculo</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vOutro" type="TDec_1302Opc" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Valor outras retenções</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vDescIncond" type="TDec_1302Opc" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Valor desconto incondicionado</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vDescCond" type="TDec_1302Opc" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Valor desconto condicionado</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vISSRet" type="TDec_1302Opc" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Valor Retenção ISS</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="indISS">
																		<xs:annotation>
																			<xs:documentation>Exibilidade do ISS:1-Exigível;2-Não incidente;3-Isenção;4-Exportação;5-Imunidade;6-Exig.Susp. Judicial;7-Exig.Susp. ADM</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:enumeration value="1"/>
																				<xs:enumeration value="2"/>
																				<xs:enumeration value="3"/>
																				<xs:enumeration value="4"/>
																				<xs:enumeration value="5"/>
																				<xs:enumeration value="6"/>
																				<xs:enumeration value="7"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="cServico" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Código do serviço prestado dentro do município</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:minLength value="1"/>
																				<xs:maxLength value="20"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="cMun" type="TCodMunIBGE" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Código do Município de Incidência do Imposto</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="cPais" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Código de Pais</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:pattern value="[0-9]{1,4}"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="nProcesso" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Número do Processo administrativo ou judicial de suspenção do processo</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:minLength value="1"/>
																				<xs:maxLength value="30"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="indIncentivo">
																		<xs:annotation>
																			<xs:documentation>Indicador de Incentivo Fiscal. 1=Sim; 2=Não</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:enumeration value="1"/>
																				<xs:enumeration value="2"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																</xs:sequence>
															</xs:complexType>
														</xs:element>
													</xs:sequence>
												</xs:choice>
												<xs:element name="PIS" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Dados do PIS</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:choice>
															<xs:element name="PISAliq">
																<xs:annotation>
																	<xs:documentation>Código de Situação Tributária do PIS.
 01 – Operação Tributável - Base de Cálculo = Valor da Operação Alíquota Normal (Cumulativo/Não Cumulativo);
02 - Operação Tributável - Base de Calculo = Valor da Operação (Alíquota Diferenciada);</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="CST">
																			<xs:annotation>
																				<xs:documentation>Código de Situação Tributária do PIS.
 01 – Operação Tributável - Base de Cálculo = Valor da Operação Alíquota Normal (Cumulativo/Não Cumulativo);
02 - Operação Tributável - Base de Calculo = Valor da Operação (Alíquota Diferenciada);</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:enumeration value="01"/>
																					<xs:enumeration value="02"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:element name="vBC" type="TDec_1302">
																			<xs:annotation>
																				<xs:documentation>Valor da BC do PIS</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="pPIS" type="TDec_0302a04">
																			<xs:annotation>
																				<xs:documentation>Alíquota do PIS (em percentual)</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="vPIS" type="TDec_1302">
																			<xs:annotation>
																				<xs:documentation>Valor do PIS</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
															<xs:element name="PISQtde">
																<xs:annotation>
																	<xs:documentation>Código de Situação Tributária do PIS.
03 - Operação Tributável - Base de Calculo = Quantidade Vendida x Alíquota por Unidade de Produto;</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="CST">
																			<xs:annotation>
																				<xs:documentation>Código de Situação Tributária do PIS.
03 - Operação Tributável - Base de Calculo = Quantidade Vendida x Alíquota por Unidade de Produto;</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:enumeration value="03"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:element name="qBCProd" type="TDec_1204v">
																			<xs:annotation>
																				<xs:documentation>Quantidade Vendida  (NT2011/004)</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="vAliqProd" type="TDec_1104v">
																			<xs:annotation>
																				<xs:documentation>Alíquota do PIS (em reais) (NT2011/004)</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="vPIS" type="TDec_1302">
																			<xs:annotation>
																				<xs:documentation>Valor do PIS</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
															<xs:element name="PISNT">
																<xs:annotation>
																	<xs:documentation>Código de Situação Tributária do PIS.
04 - Operação Tributável - Tributação Monofásica - (Alíquota Zero);
06 - Operação Tributável - Alíquota Zero;
07 - Operação Isenta da contribuição;
08 - Operação Sem Incidência da contribuição;
09 - Operação com suspensão da contribuição;</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="CST">
																			<xs:annotation>
																				<xs:documentation>Código de Situação Tributária do PIS.
04 - Operação Tributável - Tributação Monofásica - (Alíquota Zero);
05 - Operação Tributável (ST);
06 - Operação Tributável - Alíquota Zero;
07 - Operação Isenta da contribuição;
08 - Operação Sem Incidência da contribuição;
09 - Operação com suspensão da contribuição;</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:enumeration value="04"/>
																					<xs:enumeration value="05"/>
																					<xs:enumeration value="06"/>
																					<xs:enumeration value="07"/>
																					<xs:enumeration value="08"/>
																					<xs:enumeration value="09"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
															<xs:element name="PISOutr">
																<xs:annotation>
																	<xs:documentation>Código de Situação Tributária do PIS.
99 - Outras Operações.</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="CST">
																			<xs:annotation>
																				<xs:documentation>Código de Situação Tributária do PIS.
99 - Outras Operações.</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:enumeration value="49"/>
																					<xs:enumeration value="50"/>
																					<xs:enumeration value="51"/>
																					<xs:enumeration value="52"/>
																					<xs:enumeration value="53"/>
																					<xs:enumeration value="54"/>
																					<xs:enumeration value="55"/>
																					<xs:enumeration value="56"/>
																					<xs:enumeration value="60"/>
																					<xs:enumeration value="61"/>
																					<xs:enumeration value="62"/>
																					<xs:enumeration value="63"/>
																					<xs:enumeration value="64"/>
																					<xs:enumeration value="65"/>
																					<xs:enumeration value="66"/>
																					<xs:enumeration value="67"/>
																					<xs:enumeration value="70"/>
																					<xs:enumeration value="71"/>
																					<xs:enumeration value="72"/>
																					<xs:enumeration value="73"/>
																					<xs:enumeration value="74"/>
																					<xs:enumeration value="75"/>
																					<xs:enumeration value="98"/>
																					<xs:enumeration value="99"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:choice>
																			<xs:sequence>
																				<xs:element name="vBC" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do PIS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pPIS" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do PIS (em percentual)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																			<xs:sequence>
																				<xs:element name="qBCProd" type="TDec_1204v">
																					<xs:annotation>
																						<xs:documentation>Quantidade Vendida (NT2011/004) </xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vAliqProd" type="TDec_1104v">
																					<xs:annotation>
																						<xs:documentation>Alíquota do PIS (em reais) (NT2011/004)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:choice>
																		<xs:element name="vPIS" type="TDec_1302">
																			<xs:annotation>
																				<xs:documentation>Valor do PIS</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
														</xs:choice>
													</xs:complexType>
												</xs:element>
												<xs:element name="PISST" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Dados do PIS Substituição Tributária</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:choice>
																<xs:sequence>
																	<xs:element name="vBC" type="TDec_1302Opc">
																		<xs:annotation>
																			<xs:documentation>Valor da BC do PIS ST</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="pPIS" type="TDec_0302a04">
																		<xs:annotation>
																			<xs:documentation>Alíquota do PIS ST (em percentual)</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:sequence>
																<xs:sequence>
																	<xs:element name="qBCProd" type="TDec_1204">
																		<xs:annotation>
																			<xs:documentation>Quantidade Vendida </xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vAliqProd" type="TDec_1104">
																		<xs:annotation>
																			<xs:documentation>Alíquota do PIS ST (em reais)</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:sequence>
															</xs:choice>
															<xs:element name="vPIS" type="TDec_1302">
																<xs:annotation>
																	<xs:documentation>Valor do PIS ST</xs:documentation>
																</xs:annotation>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
												<xs:element name="COFINS" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Dados do COFINS</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:choice>
															<xs:element name="COFINSAliq">
																<xs:annotation>
																	<xs:documentation>Código de Situação Tributária do COFINS.
 01 – Operação Tributável - Base de Cálculo = Valor da Operação Alíquota Normal (Cumulativo/Não Cumulativo);
02 - Operação Tributável - Base de Calculo = Valor da Operação (Alíquota Diferenciada);</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="CST">
																			<xs:annotation>
																				<xs:documentation>Código de Situação Tributária do COFINS.
 01 – Operação Tributável - Base de Cálculo = Valor da Operação Alíquota Normal (Cumulativo/Não Cumulativo);
02 - Operação Tributável - Base de Calculo = Valor da Operação (Alíquota Diferenciada);</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:enumeration value="01"/>
																					<xs:enumeration value="02"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:element name="vBC" type="TDec_1302">
																			<xs:annotation>
																				<xs:documentation>Valor da BC do COFINS</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="pCOFINS" type="TDec_0302a04">
																			<xs:annotation>
																				<xs:documentation>Alíquota do COFINS (em percentual)</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="vCOFINS" type="TDec_1302">
																			<xs:annotation>
																				<xs:documentation>Valor do COFINS</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
															<xs:element name="COFINSQtde">
																<xs:annotation>
																	<xs:documentation>Código de Situação Tributária do COFINS.
03 - Operação Tributável - Base de Calculo = Quantidade Vendida x Alíquota por Unidade de Produto;</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="CST">
																			<xs:annotation>
																				<xs:documentation>Código de Situação Tributária do COFINS.
03 - Operação Tributável - Base de Calculo = Quantidade Vendida x Alíquota por Unidade de Produto;</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:enumeration value="03"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:element name="qBCProd" type="TDec_1204v">
																			<xs:annotation>
																				<xs:documentation>Quantidade Vendida (NT2011/004)</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="vAliqProd" type="TDec_1104v">
																			<xs:annotation>
																				<xs:documentation>Alíquota do COFINS (em reais) (NT2011/004)</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="vCOFINS" type="TDec_1302">
																			<xs:annotation>
																				<xs:documentation>Valor do COFINS</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
															<xs:element name="COFINSNT">
																<xs:annotation>
																	<xs:documentation>Código de Situação Tributária do COFINS:
04 - Operação Tributável - Tributação Monofásica - (Alíquota Zero);
06 - Operação Tributável - Alíquota Zero;
07 - Operação Isenta da contribuição;
08 - Operação Sem Incidência da contribuição;
09 - Operação com suspensão da contribuição;</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="CST">
																			<xs:annotation>
																				<xs:documentation>Código de Situação Tributária do COFINS:
04 - Operação Tributável - Tributação Monofásica - (Alíquota Zero);
05 - Operação Tributável (ST);
06 - Operação Tributável - Alíquota Zero;
07 - Operação Isenta da contribuição;
08 - Operação Sem Incidência da contribuição;
09 - Operação com suspensão da contribuição;</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:enumeration value="04"/>
																					<xs:enumeration value="05"/>
																					<xs:enumeration value="06"/>
																					<xs:enumeration value="07"/>
																					<xs:enumeration value="08"/>
																					<xs:enumeration value="09"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
															<xs:element name="COFINSOutr">
																<xs:annotation>
																	<xs:documentation>Código de Situação Tributária do COFINS:
49 - Outras Operações de Saída
50 - Operação com Direito a Crédito - Vinculada Exclusivamente a Receita Tributada no Mercado Interno
51 - Operação com Direito a Crédito – Vinculada Exclusivamente a Receita Não Tributada no Mercado Interno
52 - Operação com Direito a Crédito - Vinculada Exclusivamente a Receita de Exportação
53 - Operação com Direito a Crédito - Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno
54 - Operação com Direito a Crédito - Vinculada a Receitas Tributadas no Mercado Interno e de Exportação
55 - Operação com Direito a Crédito - Vinculada a Receitas Não-Tributadas no Mercado Interno e de Exportação
56 - Operação com Direito a Crédito - Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno, e de Exportação
60 - Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita Tributada no Mercado Interno
61 - Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita Não-Tributada no Mercado Interno
62 - Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita de Exportação
63 - Crédito Presumido - Operação de Aquisição Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno
64 - Crédito Presumido - Operação de Aquisição Vinculada a Receitas Tributadas no Mercado Interno e de Exportação
65 - Crédito Presumido - Operação de Aquisição Vinculada a Receitas Não-Tributadas no Mercado Interno e de Exportação
66 - Crédito Presumido - Operação de Aquisição Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno, e de Exportação
67 - Crédito Presumido - Outras Operações
70 - Operação de Aquisição sem Direito a Crédito
71 - Operação de Aquisição com Isenção
72 - Operação de Aquisição com Suspensão
73 - Operação de Aquisição a Alíquota Zero
74 - Operação de Aquisição sem Incidência da Contribuição
75 - Operação de Aquisição por Substituição Tributária
98 - Outras Operações de Entrada
99 - Outras Operações.</xs:documentation>
																</xs:annotation>
																<xs:complexType>
																	<xs:sequence>
																		<xs:element name="CST">
																			<xs:annotation>
																				<xs:documentation>Código de Situação Tributária do COFINS:
49 - Outras Operações de Saída
50 - Operação com Direito a Crédito - Vinculada Exclusivamente a Receita Tributada no Mercado Interno
51 - Operação com Direito a Crédito – Vinculada Exclusivamente a Receita Não Tributada no Mercado Interno
52 - Operação com Direito a Crédito - Vinculada Exclusivamente a Receita de Exportação
53 - Operação com Direito a Crédito - Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno
54 - Operação com Direito a Crédito - Vinculada a Receitas Tributadas no Mercado Interno e de Exportação
55 - Operação com Direito a Crédito - Vinculada a Receitas Não-Tributadas no Mercado Interno e de Exportação
56 - Operação com Direito a Crédito - Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno, e de Exportação
60 - Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita Tributada no Mercado Interno
61 - Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita Não-Tributada no Mercado Interno
62 - Crédito Presumido - Operação de Aquisição Vinculada Exclusivamente a Receita de Exportação
63 - Crédito Presumido - Operação de Aquisição Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno
64 - Crédito Presumido - Operação de Aquisição Vinculada a Receitas Tributadas no Mercado Interno e de Exportação
65 - Crédito Presumido - Operação de Aquisição Vinculada a Receitas Não-Tributadas no Mercado Interno e de Exportação
66 - Crédito Presumido - Operação de Aquisição Vinculada a Receitas Tributadas e Não-Tributadas no Mercado Interno, e de Exportação
67 - Crédito Presumido - Outras Operações
70 - Operação de Aquisição sem Direito a Crédito
71 - Operação de Aquisição com Isenção
72 - Operação de Aquisição com Suspensão
73 - Operação de Aquisição a Alíquota Zero
74 - Operação de Aquisição sem Incidência da Contribuição
75 - Operação de Aquisição por Substituição Tributária
98 - Outras Operações de Entrada
99 - Outras Operações.</xs:documentation>
																			</xs:annotation>
																			<xs:simpleType>
																				<xs:restriction base="xs:string">
																					<xs:whiteSpace value="preserve"/>
																					<xs:enumeration value="49"/>
																					<xs:enumeration value="50"/>
																					<xs:enumeration value="51"/>
																					<xs:enumeration value="52"/>
																					<xs:enumeration value="53"/>
																					<xs:enumeration value="54"/>
																					<xs:enumeration value="55"/>
																					<xs:enumeration value="56"/>
																					<xs:enumeration value="60"/>
																					<xs:enumeration value="61"/>
																					<xs:enumeration value="62"/>
																					<xs:enumeration value="63"/>
																					<xs:enumeration value="64"/>
																					<xs:enumeration value="65"/>
																					<xs:enumeration value="66"/>
																					<xs:enumeration value="67"/>
																					<xs:enumeration value="70"/>
																					<xs:enumeration value="71"/>
																					<xs:enumeration value="72"/>
																					<xs:enumeration value="73"/>
																					<xs:enumeration value="74"/>
																					<xs:enumeration value="75"/>
																					<xs:enumeration value="98"/>
																					<xs:enumeration value="99"/>
																				</xs:restriction>
																			</xs:simpleType>
																		</xs:element>
																		<xs:choice>
																			<xs:sequence>
																				<xs:element name="vBC" type="TDec_1302">
																					<xs:annotation>
																						<xs:documentation>Valor da BC do COFINS</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="pCOFINS" type="TDec_0302a04">
																					<xs:annotation>
																						<xs:documentation>Alíquota do COFINS (em percentual)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																			<xs:sequence>
																				<xs:element name="qBCProd" type="TDec_1204v">
																					<xs:annotation>
																						<xs:documentation>Quantidade Vendida (NT2011/004) </xs:documentation>
																					</xs:annotation>
																				</xs:element>
																				<xs:element name="vAliqProd" type="TDec_1104v">
																					<xs:annotation>
																						<xs:documentation>Alíquota do COFINS (em reais) (NT2011/004)</xs:documentation>
																					</xs:annotation>
																				</xs:element>
																			</xs:sequence>
																		</xs:choice>
																		<xs:element name="vCOFINS" type="TDec_1302">
																			<xs:annotation>
																				<xs:documentation>Valor do COFINS</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:sequence>
																</xs:complexType>
															</xs:element>
														</xs:choice>
													</xs:complexType>
												</xs:element>
												<xs:element name="COFINSST" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Dados do COFINS da
Substituição Tributaria;</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:choice>
																<xs:sequence>
																	<xs:element name="vBC" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor da BC do COFINS ST</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="pCOFINS" type="TDec_0302a04">
																		<xs:annotation>
																			<xs:documentation>Alíquota do COFINS ST(em percentual)</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:sequence>
																<xs:sequence>
																	<xs:element name="qBCProd" type="TDec_1204">
																		<xs:annotation>
																			<xs:documentation>Quantidade Vendida </xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vAliqProd" type="TDec_1104">
																		<xs:annotation>
																			<xs:documentation>Alíquota do COFINS ST(em reais)</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:sequence>
															</xs:choice>
															<xs:element name="vCOFINS" type="TDec_1302">
																<xs:annotation>
																	<xs:documentation>Valor do COFINS ST</xs:documentation>
																</xs:annotation>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
												<xs:element name="ICMSUFDest" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Grupo a ser informado nas vendas interestarduais para consumidor final, não contribuinte de ICMS</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:element name="vBCUFDest" type="TDec_1302">
																<xs:annotation>
																	<xs:documentation>Valor da Base de Cálculo do ICMS na UF do destinatário. </xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="pFCPUFDest" type="TDec_0302a04">
																<xs:annotation>
																	<xs:documentation>Percentual adicional inserido na alíquota interna da UF de destino, relativo ao Fundo de Combate à Pobreza (FCP) naquela UF. </xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="pICMSUFDest" type="TDec_0302a04">
																<xs:annotation>
																	<xs:documentation>Alíquota adotada nas operações internas na UF do destinatário para o produto / mercadoria.</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="pICMSInter">
																<xs:annotation>
																	<xs:documentation>Alíquota interestadual das UF envolvidas: - 4% alíquota interestadual para produtos importados; - 7% para os Estados de origem do Sul e Sudeste (exceto ES), destinado para os Estados do Norte e Nordeste  ou ES; - 12% para os demais casos.</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="xs:string">
																		<xs:whiteSpace value="preserve"/>
																		<xs:enumeration value="4.00"/>
																		<xs:enumeration value="7.00"/>
																		<xs:enumeration value="12.00"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
															<xs:element name="pICMSInterPart" type="TDec_0302a04">
																<xs:annotation>
																	<xs:documentation>Percentual de partilha para a UF do destinatário: - 40% em 2016; - 60% em 2017; - 80% em 2018; - 100% a partir de 2019.</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="vFCPUFDest" type="TDec_1302">
																<xs:annotation>
																	<xs:documentation>Valor do ICMS relativo ao Fundo de Combate à Pobreza (FCP) da UF de destino.</xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="vICMSUFDest" type="TDec_1302">
																<xs:annotation>
																	<xs:documentation>Valor do ICMS de partilha para a UF do destinatário. </xs:documentation>
																</xs:annotation>
															</xs:element>
															<xs:element name="vICMSUFRemet" type="TDec_1302">
																<xs:annotation>
																	<xs:documentation>Valor do ICMS de partilha para a UF do remetente. Nota: A partir de 2019, este valor será zero.</xs:documentation>
																</xs:annotation>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="impostoDevol" minOccurs="0">
										<xs:complexType>
											<xs:sequence>
												<xs:element name="pDevol" type="TDec_0302Max100">
													<xs:annotation>
														<xs:documentation>Percentual de mercadoria devolvida</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="IPI">
													<xs:annotation>
														<xs:documentation>Informação de IPI devolvido</xs:documentation>
													</xs:annotation>
													<xs:complexType>
														<xs:sequence>
															<xs:element name="vIPIDevol" type="TDec_1302">
																<xs:annotation>
																	<xs:documentation>Valor do IPI devolvido</xs:documentation>
																</xs:annotation>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="infAdProd" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações adicionais do produto (norma referenciada, informações complementares, etc)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="500"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
								<xs:attribute name="nItem" use="required">
									<xs:annotation>
										<xs:documentation>Número do item do NF</xs:documentation>
									</xs:annotation>
									<xs:simpleType>
										<xs:restriction base="xs:string">
											<xs:whiteSpace value="preserve"/>
											<xs:pattern value="[1-9]{1}[0-9]{0,1}|[1-8]{1}[0-9]{2}|[9]{1}[0-8]{1}[0-9]{1}|[9]{1}[9]{1}[0]{1}"/>
										</xs:restriction>
									</xs:simpleType>
								</xs:attribute>
							</xs:complexType>
						</xs:element>
						<xs:element name="total">
							<xs:annotation>
								<xs:documentation>Dados dos totais da NF-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="ICMSTot">
										<xs:annotation>
											<xs:documentation>Totais referentes ao ICMS</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="vBC" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>BC do ICMS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMS" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total do ICMS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMSDeson" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total do ICMS desonerado</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vFCPUFDest" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor total do ICMS relativo ao Fundo de Combate à Pobreza (FCP) para a UF de destino.</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMSUFDest" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor total do ICMS de partilha para a UF do destinatário</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMSUFRemet" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor total do ICMS de partilha para a UF do remetente</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vBCST" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>BC do ICMS ST</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vST" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total do ICMS ST</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vProd" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total dos produtos e serviços</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vFrete" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total do Frete</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vSeg" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total do Seguro</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vDesc" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total do Desconto</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vII" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total do II</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vIPI" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total do IPI</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vPIS" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do PIS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vCOFINS" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do COFINS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vOutro" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Outras Despesas acessórias</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vNF" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Total da NF-e</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vTotTrib" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor estimado total de impostos federais, estaduais e municipais</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="ISSQNtot" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Totais referentes ao ISSQN</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="vServ" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Total dos Serviços sob não-incidência ou não tributados pelo ICMS </xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vBC" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Base de Cálculo do ISS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vISS" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Total do ISS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vPIS" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor do PIS sobre serviços</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vCOFINS" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor do COFINS sobre serviços</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="dCompet" type="TData">
													<xs:annotation>
														<xs:documentation>Data da prestação do serviço  (AAAA-MM-DD)</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vDeducao" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor dedução para redução da base de cálculo</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vOutro" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor outras retenções</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vDescIncond" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor desconto incondicionado</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vDescCond" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor desconto condicionado</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vISSRet" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Total Retenção ISS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="cRegTrib" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Código do regime especial de tributação</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:enumeration value="1"/>
															<xs:enumeration value="2"/>
															<xs:enumeration value="3"/>
															<xs:enumeration value="4"/>
															<xs:enumeration value="5"/>
															<xs:enumeration value="6"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="retTrib" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Retenção de Tributos Federais</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="vRetPIS" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Retido de PIS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vRetCOFINS" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Retido de COFINS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vRetCSLL" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Retido de CSLL</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vBCIRRF" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Base de Cálculo do IRRF</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vIRRF" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor Retido de IRRF</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vBCRetPrev" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Base de Cálculo da Retenção da Previdêncica Social</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vRetPrev" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor da Retenção da Previdêncica Social</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="transp">
							<xs:annotation>
								<xs:documentation>Dados dos transportes da NF-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="modFrete">
										<xs:annotation>
											<xs:documentation>Modalidade do frete
0- Por conta do emitente;
1- Por conta do destinatário/remetente;
2- Por conta de terceiros;
9- Sem frete (v2.0)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="transporta" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Dados do transportador</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:choice minOccurs="0">
													<xs:element name="CNPJ" type="TCnpj">
														<xs:annotation>
															<xs:documentation>CNPJ do transportador</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="CPF" type="TCpf">
														<xs:annotation>
															<xs:documentation>CPF do transportador</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:choice>
												<xs:element name="xNome" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Razão Social ou nome do transportador</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="60"/>
															<xs:minLength value="2"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="IE" type="TIeDest" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Inscrição Estadual (v2.0)</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="xEnder" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Endereço completo</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="xMun" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Nome do munícipio</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="60"/>
															<xs:minLength value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="UF" type="TUf" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Sigla da UF</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="retTransp" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Dados da retenção  ICMS do Transporte</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="vServ" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do Serviço</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vBCRet" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>BC da Retenção do ICMS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="pICMSRet" type="TDec_0302a04">
													<xs:annotation>
														<xs:documentation>Alíquota da Retenção</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMSRet" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do ICMS Retido</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="CFOP">
													<xs:annotation>
														<xs:documentation>Código Fiscal de Operações e Prestações</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[1,2,3,5,6,7]{1}[0-9]{3}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="cMunFG" type="TCodMunIBGE">
													<xs:annotation>
														<xs:documentation>Código do Município de Ocorrência do Fato Gerador (utilizar a tabela do IBGE)</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:choice>
										<xs:sequence minOccurs="0">
											<xs:element name="veicTransp" type="TVeiculo" minOccurs="0">
												<xs:annotation>
													<xs:documentation>Dados do veículo</xs:documentation>
												</xs:annotation>
											</xs:element>
											<xs:element name="reboque" type="TVeiculo" minOccurs="0" maxOccurs="5">
												<xs:annotation>
													<xs:documentation>Dados do reboque/Dolly (v2.0)</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:sequence>
										<xs:element name="vagao" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Identificação do vagão (v2.0)</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="1"/>
													<xs:maxLength value="20"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="balsa" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Identificação da balsa (v2.0)</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="1"/>
													<xs:maxLength value="20"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:choice>
									<xs:element name="vol" minOccurs="0" maxOccurs="5000">
										<xs:annotation>
											<xs:documentation>Dados dos volumes</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="qVol" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Quantidade de volumes transportados</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[0-9]{1,15}"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="esp" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Espécie dos volumes transportados</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="marca" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Marca dos volumes transportados</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="nVol" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Numeração dos volumes transportados</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="pesoL" type="TDec_1203" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Peso líquido (em kg)</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="pesoB" type="TDec_1203" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Peso bruto (em kg)</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="lacres" minOccurs="0" maxOccurs="5000">
													<xs:complexType>
														<xs:sequence>
															<xs:element name="nLacre">
																<xs:annotation>
																	<xs:documentation>Número dos Lacres</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="TString">
																		<xs:minLength value="1"/>
																		<xs:maxLength value="60"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="cobr" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Dados da cobrança da NF-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="fat" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Dados da fatura</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="nFat" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Número da fatura</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="vOrig" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor original da fatura</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vDesc" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor do desconto da fatura</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vLiq" type="TDec_1302Opc" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor líquido da fatura</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="dup" minOccurs="0" maxOccurs="120">
										<xs:annotation>
											<xs:documentation>Dados das duplicatas NT 2011/004</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="nDup" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Número da duplicata</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="60"/>
															<xs:minLength value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="dVenc" type="TData" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Data de vencimento da duplicata (AAAA-MM-DD)</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vDup" type="TDec_1302Opc">
													<xs:annotation>
														<xs:documentation>Valor da duplicata</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="pag" minOccurs="0" maxOccurs="100">
							<xs:annotation>
								<xs:documentation>Dados de Pagamento. Obrigatório apenas para (NFC-e) NT 2012/004</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="tPag">
										<xs:annotation>
											<xs:documentation>Forma de Pagamento:01-Dinheiro;02-Cheque;03-Cartão de Crédito;04-Cartão de Débito;05-Crédito Loja;10-Vale Alimentação;11-Vale Refeição;12-Vale Presente;13-Vale Combustível;99 - Outros</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="01"/>
												<xs:enumeration value="02"/>
												<xs:enumeration value="03"/>
												<xs:enumeration value="04"/>
												<xs:enumeration value="05"/>
												<xs:enumeration value="10"/>
												<xs:enumeration value="11"/>
												<xs:enumeration value="12"/>
												<xs:enumeration value="13"/>
												<xs:enumeration value="99"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="vPag" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor do Pagamento</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="card" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Grupo de Cartões</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="tpIntegra" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Tipo de Integração do processo de pagamento com o sistema de automação da empresa/ 
																1=Pagamento integrado com o sistema de automação da empresa Ex. equipamento TEF , Comercio Eletronico
																2=Pagamento não integrado com o sistema de automação da empresa Ex: equipamento POS
														</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:enumeration value="1"/>
															<xs:enumeration value="2"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="CNPJ" type="TCnpj" minOccurs="0">
													<xs:annotation>
														<xs:documentation>CNPJ da credenciadora de cartão de crédito/débito</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="tBand" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Bandeira da operadora de cartão de crédito/débito:01–Visa; 02–Mastercard; 03–American Express; 04–Sorocred; 99–Outros</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:enumeration value="01"/>
															<xs:enumeration value="02"/>
															<xs:enumeration value="03"/>
															<xs:enumeration value="04"/>
															<xs:enumeration value="99"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="cAut" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Número de autorização da operação cartão de crédito/débito</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="20"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="infAdic" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações adicionais da NF-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="infAdFisco" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações adicionais de interesse do Fisco (v2.0)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="2000"/>
												<xs:minLength value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="infCpl" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações complementares de interesse do Contribuinte</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="5000"/>
												<xs:minLength value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="obsCont" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Campo de uso livre do contribuinte
informar o nome do campo no atributo xCampo
e o conteúdo do campo no xTexto</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xTexto">
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="xCampo" use="required">
												<xs:simpleType>
													<xs:restriction base="TString">
														<xs:minLength value="1"/>
														<xs:maxLength value="20"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
									<xs:element name="obsFisco" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Campo de uso exclusivo do Fisco
informar o nome do campo no atributo xCampo
e o conteúdo do campo no xTexto</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xTexto">
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="xCampo" use="required">
												<xs:simpleType>
													<xs:restriction base="TString">
														<xs:minLength value="1"/>
														<xs:maxLength value="20"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
									<xs:element name="procRef" minOccurs="0" maxOccurs="100">
										<xs:annotation>
											<xs:documentation>Grupo de informações do  processo referenciado</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="nProc">
													<xs:annotation>
														<xs:documentation>Indentificador do processo ou ato
concessório</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="indProc">
													<xs:annotation>
														<xs:documentation>Origem do processo, informar com:
0 - SEFAZ;
1 - Justiça Federal;
2 - Justiça Estadual;
3 - Secex/RFB;
9 - Outros</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:enumeration value="0"/>
															<xs:enumeration value="1"/>
															<xs:enumeration value="2"/>
															<xs:enumeration value="3"/>
															<xs:enumeration value="9"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="exporta" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações de exportação</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="UFSaidaPais" type="TUfEmi">
										<xs:annotation>
											<xs:documentation>Sigla da UF de Embarque ou de transposição de fronteira</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xLocExporta">
										<xs:annotation>
											<xs:documentation>Local de Embarque ou de transposição de fronteira</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xLocDespacho" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Descrição do local de despacho</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="compra" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações de compras  (Nota de Empenho, Pedido e Contrato)</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="xNEmp" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informação da Nota de Empenho de compras públicas (NT2011/004)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="22"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xPed" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informação do pedido</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xCont" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informação do contrato</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="cana" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações de registro aquisições de cana</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="safra">
										<xs:annotation>
											<xs:documentation>Identificação da safra</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="4"/>
												<xs:maxLength value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="ref">
										<xs:annotation>
											<xs:documentation>Mês e Ano de Referência, formato: MM/AAAA</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="(0[1-9]|1[0-2])([/][2][0-9][0-9][0-9])"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="forDia" maxOccurs="31">
										<xs:annotation>
											<xs:documentation>Fornecimentos diários</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="qtde" type="TDec_1110v">
													<xs:annotation>
														<xs:documentation>Quantidade em quilogramas - peso líquido</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="dia" use="required">
												<xs:annotation>
													<xs:documentation>Número do dia</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="xs:string">
														<xs:whiteSpace value="preserve"/>
														<xs:pattern value="[1-9]|[1][0-9]|[2][0-9]|[3][0-1]"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
										<xs:unique name="pk_Dia">
											<xs:selector xpath="./*"/>
											<xs:field xpath="@dia"/>
										</xs:unique>
									</xs:element>
									<xs:element name="qTotMes" type="TDec_1110v">
										<xs:annotation>
											<xs:documentation>Total do mês</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="qTotAnt" type="TDec_1110v">
										<xs:annotation>
											<xs:documentation>Total Anterior</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="qTotGer" type="TDec_1110v">
										<xs:annotation>
											<xs:documentation>Total Geral</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="deduc" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Deduções - Taxas e Contribuições</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xDed">
													<xs:annotation>
														<xs:documentation>Descrição da Dedução</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="vDed" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>valor da dedução</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="vFor" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor  dos fornecimentos</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="vTotDed" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor Total das Deduções</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="vLiqFor" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor Líquido dos fornecimentos</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="versao" type="TVerNFe" use="required">
						<xs:annotation>
							<xs:documentation>Versão do leiaute (v2.0)</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Id" use="required">
						<xs:annotation>
							<xs:documentation>PL_005d - 11/08/09 - validação do Id</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="NFe[0-9]{44}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
				<xs:unique name="pk_nItem">
					<xs:selector xpath="./*"/>
					<xs:field xpath="@nItem"/>
				</xs:unique>
			</xs:element>
			<xs:element name="infNFeSupl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Informações suplementares Nota Fiscal</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="qrCode">
							<xs:annotation>
								<xs:documentation>Texto com o QR-Code impresso no DANFE NFC-e</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:minLength value="100"/>
									<xs:maxLength value="600"/>
									<xs:pattern value="((HTTPS?|https?)://.*\?chNFe=[0-9]{44}&amp;nVersao=[0-9]{3}&amp;tpAmb=[1-2](&amp;cDest=([A-Za-z0-9.:+-/)(]{0}|[A-Za-z0-9.:+-/)(]{5,20})?)?&amp;dhEmi=[A-Fa-f0-9]{50}&amp;vNF=(0|0\.[0-9]{2}|[1-9]{1}[0-9]{0,12}(\.[0-9]{2})?)&amp;vICMS=(0|0\.[0-9]{2}|[1-9]{1}[0-9]{0,12}(\.[0-9]{2})?)&amp;digVal=[A-Fa-f0-9]{56}&amp;cIdToken=[0-9]{6}&amp;cHashQRCode=[A-Fa-f0-9]{40})"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TProtNFe">
		<xs:annotation>
			<xs:documentation>Tipo Protocolo de status resultado do processamento da NF-e</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infProt">
				<xs:annotation>
					<xs:documentation>Dados do protocolo de status</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="verAplic" type="TVerAplic">
							<xs:annotation>
								<xs:documentation>Versão do Aplicativo que processou a NF-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="chNFe" type="TChNFe">
							<xs:annotation>
								<xs:documentation>Chaves de acesso da NF-e, compostas por: UF do emitente, AAMM da emissão da NFe, CNPJ do emitente, modelo, série e número da NF-e e código numérico+DV.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="dhRecbto" type="TDateTimeUTC">
							<xs:annotation>
								<xs:documentation>Data e hora de processamento, no formato AAAA-MM-DDTHH:MM:SSTZD. Deve ser preenchida com data e hora da gravação no Banco em caso de Confirmação. Em caso de Rejeição, com data e hora do recebimento do Lote de NF-e enviado.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nProt" type="TProt" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número do Protocolo de Status da NF-e. 1 posição (1 – Secretaria de Fazenda Estadual 2 – Receita Federal); 2 - códiga da UF - 2 posições ano; 10 seqüencial no ano.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="digVal" type="ds:DigestValueType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Digest Value da NF-e processada. Utilizado para conferir a integridade da NF-e original.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cStat" type="TStat">
							<xs:annotation>
								<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xMotivo" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" type="xs:ID" use="optional"/>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerNFe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TEnviNFe">
		<xs:annotation>
			<xs:documentation> Tipo Pedido de Concessão de Autorização da Nota Fiscal Eletrônica</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="idLote" type="TIdLote"/>
			<xs:element name="indSinc">
				<xs:annotation>
					<xs:documentation>Indicador de processamento síncrono. 0=NÃO; 1=SIM=Síncrono</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:enumeration value="0"/>
						<xs:enumeration value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="NFe" type="TNFe" maxOccurs="50"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerNFe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetEnviNFe">
		<xs:annotation>
			<xs:documentation>Tipo Retorno do Pedido de Autorização da Nota Fiscal Eletrônica</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que recebeu o Lote.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>código da UF de atendimento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dhRecbto" type="TDateTimeUTC">
				<xs:annotation>
					<xs:documentation>Data e hora do recebimento, no formato AAAA-MM-DDTHH:MM:SSTZD</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:choice>
				<xs:element name="infRec" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Dados do Recibo do Lote</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="nRec" type="TRec">
								<xs:annotation>
									<xs:documentation>Número do Recibo</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="tMed" type="TMed">
								<xs:annotation>
									<xs:documentation>Tempo médio de resposta do serviço (em segundos) dos últimos 5 minutos</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="protNFe" type="TProtNFe" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Protocolo de status resultado do processamento sincrono da NFC-e </xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerNFe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TConsReciNFe">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de Consulta do Recido do Lote de Notas Fiscais Eletrônicas</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="nRec" type="TRec">
				<xs:annotation>
					<xs:documentation>Número do Recibo</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerNFe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetConsReciNFe">
		<xs:annotation>
			<xs:documentation>Tipo Retorno do Pedido de  Consulta do Recido do Lote de Notas Fiscais Eletrônicas</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que processou a NF-e</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="nRec" type="TRec">
				<xs:annotation>
					<xs:documentation>Número do Recibo Consultado</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>código da UF de atendimento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dhRecbto" type="TDateTimeUTC">
				<xs:annotation>
					<xs:documentation>Data e hora de processamento, no formato AAAA-MM-DDTHH:MM:SSTZD. Em caso de Rejeição, com data e hora do recebimento do Lote de NF-e enviado.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:sequence minOccurs="0">
				<xs:element name="cMsg">
					<xs:annotation>
						<xs:documentation>Código da Mensagem (v2.0) 
alterado para tamanho variavel 1-4. (NT2011/004)</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:pattern value="[0-9]{1,4}"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="xMsg">
					<xs:annotation>
						<xs:documentation>Mensagem da SEFAZ para o emissor. (v2.0)</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="TString">
							<xs:minLength value="1"/>
							<xs:maxLength value="200"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
			</xs:sequence>
			<xs:element name="protNFe" type="TProtNFe" minOccurs="0" maxOccurs="50">
				<xs:annotation>
					<xs:documentation>Protocolo de status resultado do processamento da NF-e</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerNFe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TNfeProc">
		<xs:annotation>
			<xs:documentation> Tipo da NF-e processada</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="NFe" type="TNFe"/>
			<xs:element name="protNFe" type="TProtNFe"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerNFe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TEndereco">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Endereço  // 24/10/08 - tamanho mínimo</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município (utilizar a tabela do IBGE), informar 9999999 para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município, informar EXTERIOR para operações com o exterior.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF, informar EX para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CEP" minOccurs="0">
				<xs:annotation>
					<xs:documentation>CEP</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{8}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cPais" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Código de Pais</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,4}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xPais" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Nome do país</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="fone" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Telefone, preencher com Código DDD + número do telefone , nas operações com exterior é permtido informar o código do país + código da localidade + número do telefone</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{6,14}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TEnderEmi">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Endereço do Emitente  // 24/10/08 - desmembrado / tamanho mínimo</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUfEmi">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CEP">
				<xs:annotation>
					<xs:documentation>CEP - NT 2011/004</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{8}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cPais" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Código do país</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:enumeration value="1058"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xPais" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Nome do país</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:enumeration value="Brasil"/>
						<xs:enumeration value="BRASIL"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="fone" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Preencher com Código DDD + número do telefone (v.2.0)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{6,14}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TLocal">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Local de Retirada ou Entrega // 24/10/08 - tamanho mínimo // v2.0</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:choice>
				<xs:element name="CNPJ" type="TCnpjOpc">
					<xs:annotation>
						<xs:documentation>CNPJ</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="CPF" type="TCpf">
					<xs:annotation>
						<xs:documentation>CPF (v2.0)</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município (utilizar a tabela do IBGE)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TVeiculo">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Veículo</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="placa">
				<xs:annotation>
					<xs:documentation>Placa do veículo (NT2011/004)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[A-Z]{2,3}[0-9]{4}|[A-Z]{3,4}[0-9]{3}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RNTC" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Registro Nacional de Transportador de Carga (ANTT)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:minLength value="1"/>
						<xs:maxLength value="20"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="Torig">
		<xs:annotation>
			<xs:documentation>Tipo Origem da mercadoria CST ICMS  origem da mercadoria: 0-Nacional exceto as indicadas nos códigos 3, 4, 5 e 8;
1-Estrangeira - Importação direta; 2-Estrangeira - Adquirida no mercado interno; 3-Nacional, conteudo superior 40% e inferior ou igual a 70%; 4-Nacional, processos produtivos básicos; 5-Nacional, conteudo inferior 40%; 6-Estrangeira - Importação direta, com similar nacional, lista CAMEX; 7-Estrangeira - mercado interno, sem simular,lista CAMEX;8-Nacional, Conteúdo de Importação superior a 70%.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="0"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="2"/>
			<xs:enumeration value="3"/>
			<xs:enumeration value="4"/>
			<xs:enumeration value="5"/>
			<xs:enumeration value="6"/>
			<xs:enumeration value="7"/>
			<xs:enumeration value="8"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TFinNFe">
		<xs:annotation>
			<xs:documentation>Tipo Finalidade da NF-e (1=Normal; 2=Complementar; 3=Ajuste; 4=Devolução/Retorno)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="2"/>
			<xs:enumeration value="3"/>
			<xs:enumeration value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TProcEmi">
		<xs:annotation>
			<xs:documentation>Tipo processo de emissão da NF-e</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="0"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="2"/>
			<xs:enumeration value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCListServ">
		<xs:annotation>
			<xs:documentation>Tipo Código da Lista de Serviços LC 116/2003</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="01.01"/>
			<xs:enumeration value="01.02"/>
			<xs:enumeration value="01.03"/>
			<xs:enumeration value="01.04"/>
			<xs:enumeration value="01.05"/>
			<xs:enumeration value="01.06"/>
			<xs:enumeration value="01.07"/>
			<xs:enumeration value="01.08"/>
			<xs:enumeration value="02.01"/>
			<xs:enumeration value="03.02"/>
			<xs:enumeration value="03.03"/>
			<xs:enumeration value="03.04"/>
			<xs:enumeration value="03.05"/>
			<xs:enumeration value="04.01"/>
			<xs:enumeration value="04.02"/>
			<xs:enumeration value="04.03"/>
			<xs:enumeration value="04.04"/>
			<xs:enumeration value="04.05"/>
			<xs:enumeration value="04.06"/>
			<xs:enumeration value="04.07"/>
			<xs:enumeration value="04.08"/>
			<xs:enumeration value="04.09"/>
			<xs:enumeration value="04.10"/>
			<xs:enumeration value="04.11"/>
			<xs:enumeration value="04.12"/>
			<xs:enumeration value="04.13"/>
			<xs:enumeration value="04.14"/>
			<xs:enumeration value="04.15"/>
			<xs:enumeration value="04.16"/>
			<xs:enumeration value="04.17"/>
			<xs:enumeration value="04.18"/>
			<xs:enumeration value="04.19"/>
			<xs:enumeration value="04.20"/>
			<xs:enumeration value="04.21"/>
			<xs:enumeration value="04.22"/>
			<xs:enumeration value="04.23"/>
			<xs:enumeration value="05.01"/>
			<xs:enumeration value="05.02"/>
			<xs:enumeration value="05.03"/>
			<xs:enumeration value="05.04"/>
			<xs:enumeration value="05.05"/>
			<xs:enumeration value="05.06"/>
			<xs:enumeration value="05.07"/>
			<xs:enumeration value="05.08"/>
			<xs:enumeration value="05.09"/>
			<xs:enumeration value="06.01"/>
			<xs:enumeration value="06.02"/>
			<xs:enumeration value="06.03"/>
			<xs:enumeration value="06.04"/>
			<xs:enumeration value="06.05"/>
			<xs:enumeration value="07.01"/>
			<xs:enumeration value="07.02"/>
			<xs:enumeration value="07.03"/>
			<xs:enumeration value="07.04"/>
			<xs:enumeration value="07.05"/>
			<xs:enumeration value="07.06"/>
			<xs:enumeration value="07.07"/>
			<xs:enumeration value="07.08"/>
			<xs:enumeration value="07.09"/>
			<xs:enumeration value="07.10"/>
			<xs:enumeration value="07.11"/>
			<xs:enumeration value="07.12"/>
			<xs:enumeration value="07.13"/>
			<xs:enumeration value="07.16"/>
			<xs:enumeration value="07.17"/>
			<xs:enumeration value="07.18"/>
			<xs:enumeration value="07.19"/>
			<xs:enumeration value="07.20"/>
			<xs:enumeration value="07.21"/>
			<xs:enumeration value="07.22"/>
			<xs:enumeration value="08.01"/>
			<xs:enumeration value="08.02"/>
			<xs:enumeration value="09.01"/>
			<xs:enumeration value="09.02"/>
			<xs:enumeration value="09.03"/>
			<xs:enumeration value="10.01"/>
			<xs:enumeration value="10.02"/>
			<xs:enumeration value="10.03"/>
			<xs:enumeration value="10.04"/>
			<xs:enumeration value="10.05"/>
			<xs:enumeration value="10.06"/>
			<xs:enumeration value="10.07"/>
			<xs:enumeration value="10.08"/>
			<xs:enumeration value="10.09"/>
			<xs:enumeration value="10.10"/>
			<xs:enumeration value="11.01"/>
			<xs:enumeration value="11.02"/>
			<xs:enumeration value="11.03"/>
			<xs:enumeration value="11.04"/>
			<xs:enumeration value="12.01"/>
			<xs:enumeration value="12.02"/>
			<xs:enumeration value="12.03"/>
			<xs:enumeration value="12.04"/>
			<xs:enumeration value="12.05"/>
			<xs:enumeration value="12.06"/>
			<xs:enumeration value="12.07"/>
			<xs:enumeration value="12.08"/>
			<xs:enumeration value="12.09"/>
			<xs:enumeration value="12.10"/>
			<xs:enumeration value="12.11"/>
			<xs:enumeration value="12.12"/>
			<xs:enumeration value="12.13"/>
			<xs:enumeration value="12.14"/>
			<xs:enumeration value="12.15"/>
			<xs:enumeration value="12.16"/>
			<xs:enumeration value="12.17"/>
			<xs:enumeration value="13.02"/>
			<xs:enumeration value="13.03"/>
			<xs:enumeration value="13.04"/>
			<xs:enumeration value="13.05"/>
			<xs:enumeration value="14.01"/>
			<xs:enumeration value="14.02"/>
			<xs:enumeration value="14.03"/>
			<xs:enumeration value="14.04"/>
			<xs:enumeration value="14.05"/>
			<xs:enumeration value="14.06"/>
			<xs:enumeration value="14.07"/>
			<xs:enumeration value="14.08"/>
			<xs:enumeration value="14.09"/>
			<xs:enumeration value="14.10"/>
			<xs:enumeration value="14.11"/>
			<xs:enumeration value="14.12"/>
			<xs:enumeration value="14.13"/>
			<xs:enumeration value="15.01"/>
			<xs:enumeration value="15.02"/>
			<xs:enumeration value="15.03"/>
			<xs:enumeration value="15.04"/>
			<xs:enumeration value="15.05"/>
			<xs:enumeration value="15.06"/>
			<xs:enumeration value="15.07"/>
			<xs:enumeration value="15.08"/>
			<xs:enumeration value="15.09"/>
			<xs:enumeration value="15.10"/>
			<xs:enumeration value="15.11"/>
			<xs:enumeration value="15.12"/>
			<xs:enumeration value="15.13"/>
			<xs:enumeration value="15.14"/>
			<xs:enumeration value="15.15"/>
			<xs:enumeration value="15.16"/>
			<xs:enumeration value="15.17"/>
			<xs:enumeration value="15.18"/>
			<xs:enumeration value="16.01"/>
			<xs:enumeration value="17.01"/>
			<xs:enumeration value="17.02"/>
			<xs:enumeration value="17.03"/>
			<xs:enumeration value="17.04"/>
			<xs:enumeration value="17.05"/>
			<xs:enumeration value="17.06"/>
			<xs:enumeration value="17.08"/>
			<xs:enumeration value="17.09"/>
			<xs:enumeration value="17.10"/>
			<xs:enumeration value="17.11"/>
			<xs:enumeration value="17.12"/>
			<xs:enumeration value="17.13"/>
			<xs:enumeration value="17.14"/>
			<xs:enumeration value="17.15"/>
			<xs:enumeration value="17.16"/>
			<xs:enumeration value="17.17"/>
			<xs:enumeration value="17.18"/>
			<xs:enumeration value="17.19"/>
			<xs:enumeration value="17.20"/>
			<xs:enumeration value="17.21"/>
			<xs:enumeration value="17.22"/>
			<xs:enumeration value="17.23"/>
			<xs:enumeration value="17.24"/>
			<xs:enumeration value="18.01"/>
			<xs:enumeration value="19.01"/>
			<xs:enumeration value="20.01"/>
			<xs:enumeration value="20.02"/>
			<xs:enumeration value="20.03"/>
			<xs:enumeration value="21.01"/>
			<xs:enumeration value="22.01"/>
			<xs:enumeration value="23.01"/>
			<xs:enumeration value="24.01"/>
			<xs:enumeration value="25.01"/>
			<xs:enumeration value="25.02"/>
			<xs:enumeration value="25.03"/>
			<xs:enumeration value="25.04"/>
			<xs:enumeration value="26.01"/>
			<xs:enumeration value="27.01"/>
			<xs:enumeration value="28.01"/>
			<xs:enumeration value="29.01"/>
			<xs:enumeration value="30.01"/>
			<xs:enumeration value="31.01"/>
			<xs:enumeration value="32.01"/>
			<xs:enumeration value="33.01"/>
			<xs:enumeration value="34.01"/>
			<xs:enumeration value="35.01"/>
			<xs:enumeration value="36.01"/>
			<xs:enumeration value="37.01"/>
			<xs:enumeration value="38.01"/>
			<xs:enumeration value="39.01"/>
			<xs:enumeration value="40.01"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIdLote">
		<xs:annotation>
			<xs:documentation> Tipo Identificação de Lote</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TVerNFe">
		<xs:annotation>
			<xs:documentation> Tipo Versão da NF-e - 3.10</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="3\.10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TGuid">
		<xs:annotation>
			<xs:documentation>Identificador único (Globally Unique Identifier)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[A-F0-9]{8}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{12}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TIpi">
		<xs:annotation>
			<xs:documentation>Tipo: Dados do IPI</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="clEnq" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Classe de Enquadramento do IPI para Cigarros e Bebidas</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:minLength value="1"/>
						<xs:maxLength value="5"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CNPJProd" type="TCnpj" minOccurs="0">
				<xs:annotation>
					<xs:documentation>CNPJ do produtor da mercadoria, quando diferente do emitente. Somente para os casos de exportação direta ou indireta.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cSelo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Código do selo de controle do IPI </xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:minLength value="1"/>
						<xs:maxLength value="60"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="qSelo" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Quantidade de selo de controle do IPI</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,12}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cEnq">
				<xs:annotation>
					<xs:documentation>Código de Enquadramento Legal do IPI (tabela a ser criada pela RFB)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:minLength value="1"/>
						<xs:maxLength value="3"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:choice>
				<xs:element name="IPITrib">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="CST">
								<xs:annotation>
									<xs:documentation>Código da Situação Tributária do IPI:
00-Entrada com recuperação de crédito
49 - Outras entradas
50-Saída tributada
99-Outras saídas</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:enumeration value="00"/>
										<xs:enumeration value="49"/>
										<xs:enumeration value="50"/>
										<xs:enumeration value="99"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:choice>
								<xs:sequence>
									<xs:element name="vBC" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor da BC do IPI</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="pIPI" type="TDec_0302a04">
										<xs:annotation>
											<xs:documentation>Alíquota do IPI</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
								<xs:sequence>
									<xs:element name="qUnid" type="TDec_1204v">
										<xs:annotation>
											<xs:documentation>Quantidade total na unidade padrão para tributação </xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="vUnid" type="TDec_1104">
										<xs:annotation>
											<xs:documentation>Valor por Unidade Tributável. Informar o valor do imposto Pauta por unidade de medida.</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:choice>
							<xs:element name="vIPI" type="TDec_1302">
								<xs:annotation>
									<xs:documentation>Valor do IPI</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="IPINT">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="CST">
								<xs:annotation>
									<xs:documentation>Código da Situação Tributária do IPI:
01-Entrada tributada com alíquota zero
02-Entrada isenta
03-Entrada não-tributada
04-Entrada imune
05-Entrada com suspensão
51-Saída tributada com alíquota zero
52-Saída isenta
53-Saída não-tributada
54-Saída imune
55-Saída com suspensão</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:enumeration value="01"/>
										<xs:enumeration value="02"/>
										<xs:enumeration value="03"/>
										<xs:enumeration value="04"/>
										<xs:enumeration value="05"/>
										<xs:enumeration value="51"/>
										<xs:enumeration value="52"/>
										<xs:enumeration value="53"/>
										<xs:enumeration value="54"/>
										<xs:enumeration value="55"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
