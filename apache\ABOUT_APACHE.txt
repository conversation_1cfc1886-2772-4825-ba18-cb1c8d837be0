
                     The Apache HTTP Server Project

                        http://httpd.apache.org/

The Apache HTTP Server Project is a collaborative software development effort
aimed at creating a robust, commercial-grade, featureful, and freely-available
source code implementation of an HTTP (Web) server. The project is jointly 
managed by a group of volunteers located around the world, using the Internet
and the Web to communicate, plan, and develop the server and its related 
documentation. In addition, hundreds of users have contributed ideas, code, 
and documentation to the project.

This file is intended to briefly describe the history of the Apache Group (as 
it was called in the early days), recognize the many contributors, and explain
how you can join the fun too.

In February of 1995, the most popular server software on the Web was the
public domain HTTP daemon developed by <PERSON> at the National Center
for Supercomputing Applications, University of Illinois, Urbana-Champaign.
However, development of that httpd had stalled after <PERSON> left NCSA in
mid-1994, and many webmasters had developed their own extensions and bug
fixes that were in need of a common distribution. A small group of these
webmasters, contacted via private e-mail, gathered together for the purpose
of coordinating their changes (in the form of "patches"). <PERSON>
and <PERSON> put together a mailing list, shared information space,
and logins for the core developers on a machine in the California Bay Area,
with bandwidth and diskspace donated by HotWired and Organic Online.
By the end of February, eight core contributors formed the foundation
of the original Apache Group:

   <PERSON>           Randy <PERSON>

with additional contributions from

   <PERSON>g<PERSON>            <PERSON>och

Using NCSA httpd 1.3 as a base, we added all of the published bug fixes
and worthwhile enhancements we could find, tested the result on our own
servers, and made the first official public release (0.6.2) of the Apache
server in April 1995. By coincidence, NCSA restarted their own development
during the same period, and Brandon Long and Beth Frank of the NCSA Server
Development Team joined the list in March as honorary members so that the
two projects could share ideas and fixes.

The early Apache server was a big hit, but we all knew that the codebase
needed a general overhaul and redesign. During May-June 1995, while
Rob Hartill and the rest of the group focused on implementing new features
for 0.7.x (like pre-forked child processes) and supporting the rapidly growing
Apache user community, Robert Thau designed a new server architecture
(code-named Shambhala) which included a modular structure and API for better
extensibility, pool-based memory allocation, and an adaptive pre-forking
process model. The group switched to this new server base in July and added
the features from 0.7.x, resulting in Apache 0.8.8 (and its brethren)
in August.

After extensive beta testing, many ports to obscure platforms, a new set
of documentation (by David Robinson), and the addition of many features
in the form of our standard modules, Apache 1.0 was released on
December 1, 1995.

Less than a year after the group was formed, the Apache server passed
NCSA's httpd as the #1 server on the Internet.

The survey by Netcraft (http://www.netcraft.com/survey/) shows that Apache
is today more widely used than all other web servers combined.

 ============================================================================

The current project management committee of the Apache HTTP Server
project (as of March, 2011) is:

    Aaron Bannert       André Malo              Astrid Stolper
    Ben Laurie          Bojan Smojver           Brad Nicholes
    Brian Havard        Brian McCallister       Chris Darroch
    Chuck Murcko        Colm MacCárthaigh       Dan Poirier
    Dirk-Willem van Gulik                       Doug MacEachern     
    Eric Covener        Erik Abele              Graham Dumpleton    
    Graham Leggett      Greg Ames               Greg Stein 
    Gregory Trubetskoy  Guenter Knauf           Issac Goldstand     
    Jeff Trawick        Jim Gallacher           Jim Jagielski      
    Joe Orton           Joe Schaefer            Joshua Slive
    Justin Erenkrantz   Ken Coar                Lars Eilebrecht
    Manoj Kasichainula  Marc Slemko             Mark J. Cox
    Martin Kraemer      Maxime Petazzoni        Nick Kew
    Nicolas Lehuen      Noirin Shirley          Paul Querna
    Philip M. Gollucci  Ralf S. Engelschall     Randy Kobes
    Rasmus Lerdorf      Rich Bowen              Roy T. Fielding
    Rüdiger Plüm        Sander Striker          Sander Temm
    Stefan Fritsch      Tony Stevenson          Victor J. Orlikowski
    Wilfredo Sanchez    William A. Rowe Jr.     Yoshiki Hayashi     

Other major contributors

   Howard Fear (mod_include), Florent Guillaume (language negotiation),
   Koen Holtman (rewrite of mod_negotiation),
   Kevin Hughes (creator of all those nifty icons),
   Brandon Long and Beth Frank (NCSA Server Development Team, post-1.3),
   Ambarish Malpani (Beginning of the NT port),
   Rob McCool (original author of the NCSA httpd 1.3),
   Paul Richards (convinced the group to use remote CVS after 1.0),
   Garey Smiley (OS/2 port), Henry Spencer (author of the regex library).

Many 3rd-party modules, frequently used and recommended, are also
freely-available and linked from the related projects page:
<http://modules.apache.org/>, and their authors frequently
contribute ideas, patches, and testing.

Hundreds of people have made individual contributions to the Apache
project. Patch contributors are listed in the CHANGES file.

 ============================================================================

How to become involved in the Apache project

There are several levels of contributing. If you just want to send
in an occasional suggestion/fix, then you can just use the bug reporting
form at <http://httpd.apache.org/bug_report.html>. You can also subscribe
to the announcements mailing list (<EMAIL>) which
we use to broadcast information about new releases, bugfixes, and upcoming
events. There's a lot of information about the development process (much of
it in serious need of updating) to be found at <http://httpd.apache.org/dev/>.

If you'd like to become an active contributor to the Apache project (the
group of volunteers who vote on changes to the distributed server), then
you need to start by subscribing <NAME_EMAIL> mailing list.
One warning though: traffic is high, 1000 to 1500 messages/month.
To subscribe to the list, send an <NAME_EMAIL>.
We recommend reading the list for a while before trying to jump in to 
development.

   NOTE: The developer mailing list (<EMAIL>) is not
   a user support forum; it is for people actively working on development
   of the server code and documentation, and for planning future
   directions. If you have user/configuration questions, send them
   to users list <http://httpd.apache.org/userslist> or to the USENET
   newsgroup "comp.infosystems.www.servers.unix".or for windows users,
   the newsgroup "comp.infosystems.www.servers.ms-windows".

There is a core group of contributors (informally called the "core")
which was formed from the project founders and is augmented from time
to time when core members nominate outstanding contributors and the
rest of the core members agree. The core group focus is more on
"business" issues and limited-circulation things like security problems
than on mainstream code development. The term "The Apache Group"
technically refers to this core of project contributors.

The Apache project is a meritocracy--the more work you have done, the more
you are allowed to do. The group founders set the original rules, but
they can be changed by vote of the active members. There is a group
of people who have logins on our server (apache.org) and access to the
svn repository.  Everyone has access to the svn snapshots.  Changes to
the code are proposed on the mailing list and usually voted on by active
members--three +1 (yes votes) and no -1 (no votes, or vetoes) are needed
to commit a code change during a release cycle; docs are usually committed
first and then changed as needed, with conflicts resolved by majority vote.

Our primary method of communication is our mailing list. Approximately 40
messages a day flow over the list, and are typically very conversational in
tone. We discuss new features to add, bug fixes, user problems, developments
in the web server community, release dates, etc. The actual code development
takes place on the developers' local machines, with proposed changes
communicated using a patch (output of a unified "diff -u oldfile newfile"
command), and committed to the source repository by one of the core
developers using remote svn.  Anyone on the mailing list can vote on a
particular issue, but we only count those made by active members or people
who are known to be experts on that part of the server. Vetoes must be
accompanied by a convincing explanation.

New members of the Apache Group are added when a frequent contributor is
nominated by one member and unanimously approved by the voting members.
In most cases, this "new" member has been actively contributing to the
group's work for over six months, so it's usually an easy decision.

The above describes our past and current (as of July 2000) guidelines,
which will probably change over time as the membership of the group
changes and our development/coordination tools improve.

 ============================================================================

The Apache Software Foundation (www.apache.org)

The Apache Software Foundation exists to provide organizational, legal,
and financial support for the Apache open-source software projects.
Founded in June 1999 by the Apache Group, the Foundation has been
incorporated as a membership-based, not-for-profit corporation in order
to ensure that the Apache projects continue to exist beyond the participation
of individual volunteers, to enable contributions of intellectual property
and funds on a sound basis, and to provide a vehicle for limiting legal
exposure while participating in open-source software projects. 

You are invited to participate in The Apache Software Foundation. We welcome
contributions in many forms. Our membership consists of those individuals
who have demonstrated a commitment to collaborative open-source software
development through sustained participation and contributions within the
Foundation's projects. Many people and companies have contributed towards
the success of the Apache projects. 

 ============================================================================

Why The Apache HTTP Server Is Free

Apache HTTP Server exists to provide a robust and commercial-grade reference
implementation of the HTTP protocol. It must remain a platform upon which
individuals and institutions can build reliable systems, both for
experimental purposes and for mission-critical purposes. We believe the
tools of online publishing should be in the hands of everyone, and
software companies should make their money providing value-added services
such as specialized modules and support, amongst other things. We realize
that it is often seen as an economic advantage for one company to "own" a
market - in the software industry that means to control tightly a
particular conduit such that all others must pay. This is typically done
by "owning" the protocols through which companies conduct business, at the
expense of all those other companies. To the extent that the protocols of
the World Wide Web remain "unowned" by a single company, the Web will
remain a level playing field for companies large and small. Thus,
"ownership" of the protocol must be prevented, and the existence of a
robust reference implementation of the protocol, available absolutely for
free to all companies, is a tremendously good thing. 

Furthermore, Apache httpd is an organic entity; those who benefit from it
by using it often contribute back to it by providing feature enhancements,
bug fixes, and support for others in public newsgroups. The amount of
effort expended by any particular individual is usually fairly light, but
the resulting product is made very strong. This kind of community can
only happen with freeware--when someone pays for software, they usually
aren't willing to fix its bugs. One can argue, then, that Apache's
strength comes from the fact that it's free, and if it were made "not
free" it would suffer tremendously, even if that money were spent on a
real development team.

We want to see Apache httpd used very widely--by large companies, small
companies, research institutions, schools, individuals, in the intranet
environment, everywhere--even though this may mean that companies who
could afford commercial software, and would pay for it without blinking,
might get a "free ride" by using Apache httpd. We would even be happy if 
some commercial software companies completely dropped their own HTTP server
development plans and used Apache httpd as a base, with the proper attributions
as described in the LICENSE file.

Thanks for using Apache HTTP Server!

