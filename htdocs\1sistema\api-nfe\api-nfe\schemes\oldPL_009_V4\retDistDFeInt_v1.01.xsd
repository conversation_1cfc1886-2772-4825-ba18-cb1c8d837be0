<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
  <xs:include schemaLocation="tiposDistDFe_v1.01.xsd"/>
  <xs:element name="retDistDFeInt">
    <xs:annotation>
      <xs:documentation>Schema do resultado do pedido de distribuição de DF-e de interesse</xs:documentation>
    </xs:annotation>
    <xs:complexType>
      <xs:sequence>
        <xs:element name="tpAmb" type="TAmb">
          <xs:annotation>
            <xs:documentation>
            Identificação do Ambiente:
            1 - Produção
            2 - Homologação
            </xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="verAplic" type="TVerAplic">
          <xs:annotation>
            <xs:documentation>Versão do Web Service NFeDistribuicaoDFe</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="cStat" type="TStat">
          <xs:annotation>
            <xs:documentation>Código do status de processamento da requisição</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="xMotivo" type="TMotivo">
          <xs:annotation>
            <xs:documentation>Descrição literal do status do processamento da requisição</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="dhResp" type="TDateTimeUTC">
          <xs:annotation>
            <xs:documentation>Data e Hora de processamento da requisição no formato AAAA-MM-DDTHH:MM:SSTZD</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="ultNSU" type="TNSU">
          <xs:annotation>
            <xs:documentation>Último NSU pesquisado no Ambiente Nacional. Se for o caso, o solicitante pode continuar a consulta a partir deste NSU para obter novos resultados.</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="maxNSU" type="TNSU">
          <xs:annotation>
            <xs:documentation>Maior NSU existente no Ambiente Nacional para o CNPJ/CPF informado</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="loteDistDFeInt" minOccurs="0">
          <xs:annotation>
            <xs:documentation>Conjunto de informações resumidas e documentos fiscais eletrônicos de interesse da pessoa ou empresa. </xs:documentation>
          </xs:annotation>
          <xs:complexType>
            <xs:sequence maxOccurs="50">
              <xs:element name="docZip">
                <xs:annotation>
                  <xs:documentation>Informação resumida ou documento fiscal eletrônico de interesse da pessoa ou empresa. O conteúdo desta tag estará compactado no padrão gZip. O tipo do campo é base64Binary.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                  <xs:simpleContent>
                    <xs:extension base="xs:base64Binary">
                      <xs:attribute name="NSU" type="TNSU" use="required">
                        <xs:annotation>
                          <xs:documentation>NSU do documento fiscal</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                      <xs:attribute name="schema" type="xs:string" use="required">
                        <xs:annotation>
                          <xs:documentation>Identificação do Schema XML que será utilizado para validar o XML existente no conteúdo da tag docZip. Vai identificar o tipo do documento e sua versão. Exemplos: resNFe_v1.00.xsd, procNFe_v3.10.xsd, resEvento_1.00.xsd, procEventoNFe_v1.00.xsd</xs:documentation>
                        </xs:annotation>
                      </xs:attribute>
                    </xs:extension>
                  </xs:simpleContent>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="versao" type="TVerDistDFe" use="required"/>
    </xs:complexType>
  </xs:element>
</xs:schema>



