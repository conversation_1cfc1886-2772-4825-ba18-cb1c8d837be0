<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2005 rel. 3 U (http://www.altova.com) by admin (.) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/nfe" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/nfe" elementFormDefault="qualified" attributeFormDefault="unqualified">
  <xs:include schemaLocation="tiposBasico_v1.03.xsd"/>
  <xs:element name="detEvento">
    <xs:annotation>
      <xs:documentation>Schema XML de validação do evento de averbação da NFe (e790700)</xs:documentation>
    </xs:annotation>
    <xs:complexType>
      <xs:sequence>
        <xs:element name="descEvento">
          <xs:annotation>
            <xs:documentation>Descrição do Evento - “Averbação para Exportação”</xs:documentation>
          </xs:annotation>
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:whiteSpace value="preserve"/>
              <xs:enumeration value="Averbação para Exportação"/>
              <xs:enumeration value="Averbacao para Exportacao"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element name="tpAutor">
          <xs:annotation>
            <xs:documentation>Tipo do Autor do Evento (6=RFB)</xs:documentation>
          </xs:annotation>
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:whiteSpace value="preserve"/>
              <xs:enumeration value="6"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element name="verAplic" type="TVerAplic">
          <xs:annotation>
            <xs:documentation>Versão do aplicativo do autor do evento</xs:documentation>
          </xs:annotation>
        </xs:element>
        <xs:element name="itensAverbados"  minOccurs="1" maxOccurs="990" >
          <xs:annotation>
            <xs:documentation>Informações dos itens da NF-e do evento.</xs:documentation>
          </xs:annotation>
          <xs:complexType>
            <xs:sequence>
              <xs:element name="dhEmbarque" type="TDateTimeUTC" >
                <xs:annotation>
                  <xs:documentation>Data do Embarque no formato AAAA-MM-DDThh:mm:ssTZD</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element name="dhAverbacao" type="TDateTimeUTC" >
                <xs:annotation>
                  <xs:documentation>Data da averbação no formato AAAA-MM-DDThh:mm:ssTZD</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element name="nDue" >
                <xs:annotation>
                  <xs:documentation>Número Identificador da Declaração Única do Comércio Exterior associada</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:whiteSpace value="preserve"/>
                    <xs:pattern value="[0-9]{14}"/>
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="nItem" >
                <xs:annotation>
                  <xs:documentation>Número do item da NF-e averbada</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:whiteSpace value="preserve"/>
                    <xs:pattern value="[0-9]{1,3}"/>
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="nItemDue" >
                <xs:annotation>
                  <xs:documentation>Informação do número do item na Declaração de Exportação associada a averbação.</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:whiteSpace value="preserve"/>
                    <xs:pattern value="[0-9]{1,3}"/>
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="qItem" type="TDec_1104v">
                <xs:annotation>
                  <xs:documentation>Quantidade averbada do item na unidade tributária (campo uTrib)</xs:documentation>
                </xs:annotation>
              </xs:element>
              <xs:element name="motAlteracao" minOccurs="0">
                <xs:annotation>
                  <xs:documentation>
                    Motivo da Alteração
                    1 - Exportação Averbada;
                    2 - Diminuição da Quantidade Averbada;
                    3 - Cancelamento da Exportação;
                  </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:whiteSpace value="preserve"/>
                    <xs:enumeration value="1" />
                    <xs:enumeration value="2" />
                    <xs:enumeration value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="versao" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="preserve"/>
            <xs:enumeration value="1\.00"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>
</xs:schema>
